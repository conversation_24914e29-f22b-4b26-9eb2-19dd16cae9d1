#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局模型客户端

提供统一的 LLM 客户端配置和管理
"""

import os
import logging
from dotenv import load_dotenv

# 尝试导入AutoGen，如果失败则使用简化实现
try:
    from autogen_core.models import ModelFamily
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)


def get_llm_client():
    """
    获取 LLM 客户端

    Returns:
        OpenAIChatCompletionClient: 配置好的模型客户端

    Raises:
        ValueError: 当 API Key 未配置时抛出
    """
    # 从环境变量获取配置
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.deepseek.com/v1")
    model = os.getenv("OPENAI_MODEL", "deepseek-chat")

    # 检查 API Key
    if not api_key:
        raise ValueError(
            "OPENAI_API_KEY 未配置。请在 .env 文件中设置：\n"
            "OPENAI_API_KEY=your_api_key_here"
        )

    # 创建客户端
    llm_client = OpenAIChatCompletionClient(
        base_url=base_url,
        model=model,
        api_key=api_key,
        temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.7")),
        max_tokens=int(os.getenv("OPENAI_MAX_TOKENS", "2048")),
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "multiple_system_messages": True,
            "structured_output": True
        }
    )

    logger.info(f"LLM 客户端已创建: {model} @ {base_url}")
    return llm_client


class SimpleLLMClient:
    """简化的LLM客户端 - 当AutoGen不可用时使用"""

    def __init__(self):
        self.model = "simple-llm"
        logger.info("使用简化LLM客户端")

    async def generate_response(self, prompt: str) -> str:
        """生成简化响应"""
        return "✅ 处理完成"


# 全局模型客户端实例
try:
    if AUTOGEN_AVAILABLE:
        model_client = get_llm_client()
        logger.info("✅ AutoGen LLM客户端初始化成功")
    else:
        model_client = SimpleLLMClient()
        logger.info("✅ 简化LLM客户端初始化成功")
except Exception as e:
    logger.warning(f"LLM客户端初始化失败: {e}，使用简化客户端")
    model_client = SimpleLLMClient()

