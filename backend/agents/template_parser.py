#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板解析智能体

负责解析用户提供的订单模板，提取结构化数据
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

logger = logging.getLogger(__name__)


class TemplateParserAgent:
    """模板解析智能体"""
    
    def __init__(self, model_client: OpenAIChatCompletionClient, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager
        self.agent: Optional[AssistantAgent] = None
        
    async def initialize(self):
        """初始化智能体"""
        logger.info("📋 初始化模板解析智能体...")
        
        try:
            # 创建模板解析工具函数
            async def parse_json_template(template_str: str) -> str:
                """解析JSON格式的模板"""
                try:
                    parsed = json.loads(template_str)
                    return f"✅ JSON模板解析成功，包含 {len(parsed)} 个字段"
                except json.JSONDecodeError as e:
                    return f"❌ JSON格式错误: {str(e)}"
            
            async def extract_order_fields(text: str) -> str:
                """从文本中提取订单字段"""
                # 简单的字段提取逻辑
                fields = []
                lines = text.split('\n')
                
                for line in lines:
                    line = line.strip()
                    if ':' in line or '：' in line:
                        fields.append(line)
                
                if fields:
                    return f"✅ 提取到 {len(fields)} 个字段:\n" + "\n".join(fields)
                else:
                    return "⚠️ 未找到明显的字段结构"
            
            async def validate_template_structure(template_data: str) -> str:
                """验证模板结构"""
                required_fields = ['customer_code', 'goods_list']
                missing_fields = []
                
                for field in required_fields:
                    if field not in template_data.lower():
                        missing_fields.append(field)
                
                if missing_fields:
                    return f"⚠️ 缺少必要字段: {', '.join(missing_fields)}"
                else:
                    return "✅ 模板结构验证通过"
            
            # 创建智能体
            self.agent = AssistantAgent(
                name="模板解析智能体",
                model_client=self.model_client,
                system_message="""你是洞车系统的模板解析专家，负责解析和理解用户提供的订单模板。

你的主要职责：
1. 解析各种格式的订单模板（JSON、文本、表格等）
2. 提取关键字段和数据结构
3. 验证模板的完整性和正确性
4. 将非结构化数据转换为结构化格式

支持的模板格式：
- JSON格式订单模板
- 文本格式的字段列表
- 表格形式的数据
- 自然语言描述的订单信息

输出要求：
- 提供清晰的解析结果
- 指出缺失或错误的字段
- 建议改进方案
- 生成标准化的数据结构

请用中文回复，保持专业和准确。""",
                tools=[
                    parse_json_template,
                    extract_order_fields,
                    validate_template_structure
                ]
            )
            
            logger.info("✅ 模板解析智能体初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 模板解析智能体初始化失败: {e}")
            raise
    
    async def parse_template(self, template_data: str, session_id: str) -> Dict[str, Any]:
        """解析模板"""
        if not self.agent:
            raise RuntimeError("模板解析智能体未初始化")
        
        try:
            # 保存原始模板到记忆
            await self.memory_manager.add_context(session_id, "original_template", template_data)
            
            # 构建解析请求
            parse_request = f"""请解析以下订单模板：

{template_data}

请执行以下任务：
1. 识别模板格式（JSON、文本、表格等）
2. 提取所有字段和数据
3. 验证模板结构的完整性
4. 生成标准化的数据结构
5. 指出任何问题或建议

请提供详细的解析结果。"""
            
            # 使用智能体解析
            result = await self.agent.run(task=parse_request)
            
            # 处理结果
            if result.messages:
                parse_result = result.messages[-1].content
                
                # 尝试提取结构化数据
                structured_data = await self._extract_structured_data(template_data, parse_result)
                
                # 保存解析结果到记忆
                await self.memory_manager.add_result(session_id, "template_parser", {
                    "original_template": template_data,
                    "parse_result": parse_result,
                    "structured_data": structured_data,
                    "timestamp": datetime.now().isoformat()
                })
                
                return {
                    "success": True,
                    "parse_result": parse_result,
                    "structured_data": structured_data,
                    "agent_name": "模板解析智能体"
                }
            else:
                return {
                    "success": False,
                    "error": "智能体没有返回解析结果",
                    "agent_name": "模板解析智能体"
                }
                
        except Exception as e:
            logger.error(f"模板解析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_name": "模板解析智能体"
            }
    
    async def _extract_structured_data(self, template_data: str, parse_result: str) -> Dict[str, Any]:
        """从解析结果中提取结构化数据"""
        structured_data = {
            "customer_info": {},
            "goods_list": [],
            "order_info": {},
            "metadata": {}
        }
        
        try:
            # 尝试解析JSON格式
            if template_data.strip().startswith('{') and template_data.strip().endswith('}'):
                json_data = json.loads(template_data)
                
                # 提取客户信息
                if 'customer_code' in json_data:
                    structured_data["customer_info"]["customer_code"] = json_data["customer_code"]
                if 'customer_name' in json_data:
                    structured_data["customer_info"]["customer_name"] = json_data["customer_name"]
                
                # 提取商品列表
                if 'goods_list' in json_data:
                    structured_data["goods_list"] = json_data["goods_list"]
                elif 'goods' in json_data:
                    structured_data["goods_list"] = json_data["goods"]
                
                # 提取订单信息
                order_fields = ['order_type', 'priority', 'delivery_date', 'notes']
                for field in order_fields:
                    if field in json_data:
                        structured_data["order_info"][field] = json_data[field]
                
                # 其他字段作为元数据
                for key, value in json_data.items():
                    if key not in ['customer_code', 'customer_name', 'goods_list', 'goods'] + order_fields:
                        structured_data["metadata"][key] = value
            
            else:
                # 处理文本格式
                lines = template_data.split('\n')
                current_section = "metadata"
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 检测段落
                    if '客户' in line or 'customer' in line.lower():
                        current_section = "customer_info"
                        continue
                    elif '商品' in line or 'goods' in line.lower():
                        current_section = "goods_list"
                        continue
                    elif '订单' in line or 'order' in line.lower():
                        current_section = "order_info"
                        continue
                    
                    # 提取键值对
                    if ':' in line or '：' in line:
                        separator = ':' if ':' in line else '：'
                        key, value = line.split(separator, 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if current_section == "goods_list":
                            # 商品信息作为列表项
                            structured_data["goods_list"].append({
                                "name": key,
                                "info": value
                            })
                        else:
                            structured_data[current_section][key] = value
            
        except Exception as e:
            logger.warning(f"结构化数据提取失败: {e}")
            # 如果提取失败，至少保存原始数据
            structured_data["metadata"]["original_template"] = template_data
            structured_data["metadata"]["parse_error"] = str(e)
        
        return structured_data
    
    async def get_template_suggestions(self, partial_template: str) -> List[str]:
        """获取模板建议"""
        suggestions = [
            "添加客户编码字段 (customer_code)",
            "添加商品列表字段 (goods_list)",
            "添加订单类型字段 (order_type)",
            "添加交付日期字段 (delivery_date)",
            "使用JSON格式以提高解析准确性"
        ]
        
        # 根据现有内容过滤建议
        if 'customer_code' in partial_template.lower():
            suggestions = [s for s in suggestions if 'customer_code' not in s]
        
        if 'goods_list' in partial_template.lower() or '商品' in partial_template:
            suggestions = [s for s in suggestions if 'goods_list' not in s]
        
        return suggestions
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理模板解析智能体资源...")
        self.agent = None
        logger.info("✅ 模板解析智能体资源清理完成")
