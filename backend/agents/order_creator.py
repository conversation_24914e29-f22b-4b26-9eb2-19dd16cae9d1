#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单创建智能体

负责调用洞车API创建实际订单
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 导入洞车API工具
from ..qtools import (
    create_order_rk,
    query_order_goods,
    DongCheAPIError
)

logger = logging.getLogger(__name__)


class OrderCreatorAgent:
    """订单创建智能体"""
    
    def __init__(self, model_client: OpenAIChatCompletionClient, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager
        self.agent: Optional[AssistantAgent] = None
        
    async def initialize(self):
        """初始化智能体"""
        logger.info("📦 初始化订单创建智能体...")
        
        try:
            # 创建订单创建工具函数
            async def create_inbound_order(order_data_str: str) -> str:
                """创建入库订单"""
                try:
                    # 解析订单数据
                    if isinstance(order_data_str, str):
                        order_data = json.loads(order_data_str)
                    else:
                        order_data = order_data_str
                    
                    # 调用洞车API创建订单
                    result = await create_order_rk(num=1, order_data=order_data)
                    
                    if result.get("code") == "200":
                        workflow_data = result.get("data", [])
                        if workflow_data:
                            steps = []
                            for step in workflow_data:
                                step_info = f"步骤 {step.get('index', '?')}: {step.get('workOrderTypeName', '未知操作')}"
                                steps.append(step_info)
                            
                            return f"✅ 订单创建成功！工单流程:\n" + "\n".join(steps)
                        else:
                            return "✅ 订单创建成功，但未返回工单流程信息"
                    else:
                        return f"❌ 订单创建失败: {result.get('message', '未知错误')}"
                        
                except json.JSONDecodeError as e:
                    return f"❌ 订单数据格式错误: {str(e)}"
                except DongCheAPIError as e:
                    return f"❌ 调用洞车API失败: {str(e)}"
                except Exception as e:
                    return f"❌ 创建订单时出错: {str(e)}"
            
            async def query_order_status(order_no: str) -> str:
                """查询订单状态"""
                try:
                    result = await query_order_goods(order_no)
                    
                    if result.get("code") == "200":
                        goods_data = result.get("data", [])
                        if goods_data:
                            status_info = []
                            for goods in goods_data[:5]:  # 只显示前5个商品
                                info = f"商品: {goods.get('skuName', '未知')}"
                                info += f", 数量: {goods.get('quantity', '未知')}"
                                info += f", 状态: {goods.get('skuStatus', '未知')}"
                                status_info.append(info)
                            
                            return f"✅ 订单 {order_no} 状态查询成功:\n" + "\n".join(status_info)
                        else:
                            return f"⚠️ 订单 {order_no} 暂无商品信息"
                    else:
                        return f"❌ 查询订单状态失败: {result.get('message', '未知错误')}"
                        
                except DongCheAPIError as e:
                    return f"❌ 查询订单状态失败: {str(e)}"
                except Exception as e:
                    return f"❌ 查询订单状态时出错: {str(e)}"
            
            async def prepare_order_data(template_data_str: str) -> str:
                """准备订单数据"""
                try:
                    if isinstance(template_data_str, str):
                        template_data = json.loads(template_data_str)
                    else:
                        template_data = template_data_str
                    
                    # 构建标准订单数据结构
                    order_data = {
                        "customerInfo": {
                            "customerCode": template_data.get("customer_code", "DCKH0028"),
                            "customerName": template_data.get("customer_name", "默认客户")
                        },
                        "goodsList": [],
                        "workOrderType": template_data.get("order_type", "入库订单"),
                        "priority": template_data.get("priority", "normal"),
                        "notes": template_data.get("notes", ""),
                        "metadata": {
                            "created_by": "洞车造数工具",
                            "created_at": datetime.now().isoformat()
                        }
                    }
                    
                    # 处理商品列表
                    goods_list = template_data.get("goods_list", [])
                    for goods in goods_list:
                        if isinstance(goods, dict):
                            goods_item = {
                                "skuName": goods.get("name", goods.get("skuName", "未知商品")),
                                "quantity": goods.get("quantity", 1),
                                "specifications": goods.get("specifications", ""),
                                "notes": goods.get("notes", "")
                            }
                            order_data["goodsList"].append(goods_item)
                        elif isinstance(goods, str):
                            # 简单字符串格式
                            order_data["goodsList"].append({
                                "skuName": goods,
                                "quantity": 1,
                                "specifications": "",
                                "notes": ""
                            })
                    
                    # 如果没有商品列表，添加默认商品
                    if not order_data["goodsList"]:
                        order_data["goodsList"].append({
                            "skuName": "默认商品",
                            "quantity": 1,
                            "specifications": "默认规格",
                            "notes": "由洞车造数工具生成"
                        })
                    
                    return f"✅ 订单数据准备完成:\n{json.dumps(order_data, ensure_ascii=False, indent=2)}"
                    
                except json.JSONDecodeError as e:
                    return f"❌ 模板数据格式错误: {str(e)}"
                except Exception as e:
                    return f"❌ 准备订单数据时出错: {str(e)}"
            
            async def validate_order_before_creation(order_data_str: str) -> str:
                """创建前验证订单"""
                try:
                    if isinstance(order_data_str, str):
                        order_data = json.loads(order_data_str)
                    else:
                        order_data = order_data_str
                    
                    validation_results = []
                    
                    # 检查必要字段
                    if "customerInfo" not in order_data:
                        validation_results.append("❌ 缺少客户信息")
                    elif not order_data["customerInfo"].get("customerCode"):
                        validation_results.append("❌ 缺少客户编码")
                    else:
                        validation_results.append("✅ 客户信息完整")
                    
                    if "goodsList" not in order_data or not order_data["goodsList"]:
                        validation_results.append("❌ 缺少商品列表")
                    else:
                        goods_count = len(order_data["goodsList"])
                        validation_results.append(f"✅ 商品列表包含 {goods_count} 个商品")
                    
                    # 检查商品详情
                    for i, goods in enumerate(order_data.get("goodsList", []), 1):
                        if not goods.get("skuName"):
                            validation_results.append(f"⚠️ 商品 {i} 缺少名称")
                        if not goods.get("quantity"):
                            validation_results.append(f"⚠️ 商品 {i} 缺少数量")
                    
                    # 整体评估
                    error_count = sum(1 for result in validation_results if result.startswith("❌"))
                    if error_count == 0:
                        validation_results.append("✅ 订单验证通过，可以创建")
                    else:
                        validation_results.append(f"❌ 发现 {error_count} 个错误，请修复后再创建")
                    
                    return "\n".join(validation_results)
                    
                except json.JSONDecodeError as e:
                    return f"❌ 订单数据格式错误: {str(e)}"
                except Exception as e:
                    return f"❌ 验证订单时出错: {str(e)}"
            
            # 创建智能体
            self.agent = AssistantAgent(
                name="订单创建智能体",
                model_client=self.model_client,
                system_message="""你是洞车系统的订单创建专家，负责将验证通过的数据转换为实际订单。

你的主要职责：
1. 准备标准化的订单数据结构
2. 验证订单数据的完整性
3. 调用洞车API创建实际订单
4. 监控订单创建状态
5. 提供订单创建结果反馈

订单创建流程：
1. 接收验证通过的数据
2. 转换为洞车系统要求的格式
3. 执行创建前的最终验证
4. 调用API创建订单
5. 返回创建结果和订单号

支持的订单类型：
- 入库订单 (默认)
- 出库订单
- 调拨订单
- 盘点订单

输出要求：
- 提供详细的创建过程
- 明确指出成功或失败原因
- 返回订单号和工单流程
- 提供后续操作建议

请用中文回复，保持专业和准确。""",
                tools=[
                    prepare_order_data,
                    validate_order_before_creation,
                    create_inbound_order,
                    query_order_status
                ]
            )
            
            logger.info("✅ 订单创建智能体初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 订单创建智能体初始化失败: {e}")
            raise
    
    async def create_order(self, order_data: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """创建订单"""
        if not self.agent:
            raise RuntimeError("订单创建智能体未初始化")
        
        try:
            # 保存订单创建请求到记忆
            await self.memory_manager.add_context(session_id, "order_creation_request", order_data)
            
            # 构建创建请求
            creation_request = f"""请创建以下订单：

{json.dumps(order_data, ensure_ascii=False, indent=2)}

请执行以下步骤：
1. 准备标准化的订单数据
2. 执行创建前验证
3. 调用洞车API创建订单
4. 返回创建结果

请提供详细的创建过程和结果。"""
            
            # 使用智能体创建订单
            result = await self.agent.run(task=creation_request)
            
            # 处理结果
            if result.messages:
                creation_result = result.messages[-1].content
                
                # 分析创建结果
                creation_summary = await self._analyze_creation_result(creation_result)
                
                # 保存创建结果到记忆
                await self.memory_manager.add_result(session_id, "order_creator", {
                    "original_request": order_data,
                    "creation_result": creation_result,
                    "creation_summary": creation_summary,
                    "timestamp": datetime.now().isoformat()
                })
                
                return {
                    "success": True,
                    "creation_result": creation_result,
                    "creation_summary": creation_summary,
                    "order_created": creation_summary.get("order_created", False),
                    "order_no": creation_summary.get("order_no"),
                    "agent_name": "订单创建智能体"
                }
            else:
                return {
                    "success": False,
                    "error": "智能体没有返回创建结果",
                    "order_created": False,
                    "agent_name": "订单创建智能体"
                }
                
        except Exception as e:
            logger.error(f"订单创建失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "order_created": False,
                "agent_name": "订单创建智能体"
            }
    
    async def _analyze_creation_result(self, creation_result: str) -> Dict[str, Any]:
        """分析创建结果"""
        summary = {
            "order_created": False,
            "order_no": None,
            "workflow_steps": [],
            "success_messages": [],
            "error_messages": [],
            "warnings": []
        }
        
        try:
            lines = creation_result.split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if '✅' in line:
                    summary["success_messages"].append(line.replace('✅', '').strip())
                    
                    # 检查是否包含订单创建成功信息
                    if '订单创建成功' in line:
                        summary["order_created"] = True
                    
                    # 提取工单流程步骤
                    if '步骤' in line and ':' in line:
                        summary["workflow_steps"].append(line.replace('✅', '').strip())
                
                elif '❌' in line:
                    summary["error_messages"].append(line.replace('❌', '').strip())
                    
                elif '⚠️' in line:
                    summary["warnings"].append(line.replace('⚠️', '').strip())
                
                # 尝试提取订单号
                if 'DC' in line and any(char.isdigit() for char in line):
                    # 简单的订单号提取逻辑
                    words = line.split()
                    for word in words:
                        if word.startswith('DC') and len(word) > 10:
                            summary["order_no"] = word
                            break
            
            # 如果有错误消息，标记为创建失败
            if summary["error_messages"]:
                summary["order_created"] = False
            
        except Exception as e:
            logger.warning(f"创建结果分析失败: {e}")
            summary["error_messages"].append(f"结果分析出错: {str(e)}")
        
        return summary
    
    async def get_order_template(self) -> Dict[str, Any]:
        """获取订单模板"""
        return {
            "customer_code": "DCKH0028",
            "customer_name": "示例客户",
            "order_type": "入库订单",
            "priority": "normal",
            "goods_list": [
                {
                    "name": "示例商品1",
                    "quantity": 10,
                    "specifications": "规格1",
                    "notes": "备注1"
                },
                {
                    "name": "示例商品2", 
                    "quantity": 5,
                    "specifications": "规格2",
                    "notes": "备注2"
                }
            ],
            "delivery_date": "2025-02-01",
            "notes": "这是一个示例订单模板"
        }
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理订单创建智能体资源...")
        self.agent = None
        logger.info("✅ 订单创建智能体资源清理完成")
