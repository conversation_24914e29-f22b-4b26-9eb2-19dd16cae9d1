#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
洞车智能体团队管理器

管理模板解析、信息验证、订单创建三个智能体的协作
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import TextMessage

from backend.llm import model_client
from backend.agents.template_parser import TemplateParserAgent
from backend.agents.info_validator import InfoValidatorAgent
from backend.agents.order_creator import OrderCreatorAgent
from backend.agents.memory_manager import MemoryManager

logger = logging.getLogger(__name__)


class DongCheTeamManager:
    """洞车智能体团队管理器"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.template_parser: Optional[TemplateParserAgent] = None
        self.info_validator: Optional[InfoValidatorAgent] = None
        self.order_creator: Optional[OrderCreatorAgent] = None
        self.coordinator: Optional[AssistantAgent] = None
        self.team: Optional[SelectorGroupChat] = None
        
    async def initialize(self):
        """初始化智能体团队"""
        logger.info("🤖 初始化智能体团队...")
        
        try:
            # 初始化各个智能体
            self.template_parser = TemplateParserAgent(model_client, self.memory_manager)
            await self.template_parser.initialize()
            
            self.info_validator = InfoValidatorAgent(model_client, self.memory_manager)
            await self.info_validator.initialize()
            
            self.order_creator = OrderCreatorAgent(model_client, self.memory_manager)
            await self.order_creator.initialize()
            
            # 创建协调者智能体
            self.coordinator = AssistantAgent(
                name="协调者",
                model_client=model_client,
                system_message="""你是洞车造数工具的协调者，负责管理整个订单创建流程。

你的职责：
1. 理解用户需求，判断需要哪些智能体参与
2. 协调各智能体的工作流程
3. 确保信息在智能体间正确传递
4. 向用户提供清晰的状态更新

工作流程：
1. 模板解析 -> 解析用户提供的订单模板
2. 信息验证 -> 验证商品、客户等信息的有效性
3. 订单创建 -> 调用API创建实际订单

请用中文回复，保持专业和友好的语调。"""
            )
            
            # 创建智能体团队
            self.team = SelectorGroupChat(
                participants=[
                    self.coordinator,
                    self.template_parser.agent,
                    self.info_validator.agent,
                    self.order_creator.agent
                ],
                model_client=model_client,
                selector_prompt="""根据当前对话内容，选择最合适的智能体来处理：

- 协调者：处理一般对话、流程协调、状态汇报
- 模板解析智能体：解析订单模板、提取结构化数据
- 信息验证智能体：验证商品信息、客户信息、数据有效性
- 订单创建智能体：创建实际订单、调用API接口

选择原则：
1. 如果涉及模板解析，选择模板解析智能体
2. 如果需要验证信息，选择信息验证智能体  
3. 如果要创建订单，选择订单创建智能体
4. 其他情况选择协调者""",
                termination_condition=MaxMessageTermination(max_messages=20)
            )
            
            logger.info("✅ 智能体团队初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 智能体团队初始化失败: {e}")
            raise
    
    async def process_message(self, message: str, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """处理用户消息"""
        if not self.team:
            raise RuntimeError("智能体团队未初始化")
        
        try:
            # 保存用户消息到记忆
            await self.memory_manager.add_message(session_id, "user", message)
            
            # 创建消息对象
            text_message = TextMessage(content=message, source="user")
            
            # 发送开始处理状态
            yield {
                "agent_name": "系统",
                "content": "🤖 智能体团队开始处理您的请求...",
                "status": "processing"
            }
            
            # 使用团队处理消息
            result = await self.team.run(task=text_message)
            
            # 处理结果
            if result.messages:
                for message_obj in result.messages:
                    agent_name = getattr(message_obj, 'source', '未知智能体')
                    content = getattr(message_obj, 'content', '')
                    
                    # 保存智能体回复到记忆
                    await self.memory_manager.add_message(session_id, agent_name, content)
                    
                    yield {
                        "agent_name": agent_name,
                        "content": content,
                        "status": "success"
                    }
            else:
                yield {
                    "agent_name": "系统",
                    "content": "抱歉，没有收到智能体的回复",
                    "status": "error"
                }
                
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            yield {
                "agent_name": "系统",
                "content": f"处理消息时出错: {str(e)}",
                "status": "error"
            }
    
    async def create_order(self, order_data: Dict[str, Any], session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """创建订单流程"""
        if not self.team:
            raise RuntimeError("智能体团队未初始化")
        
        try:
            # 保存订单请求到记忆
            await self.memory_manager.add_context(session_id, "order_request", order_data)
            
            # 构建订单创建消息
            order_message = f"""请帮我创建一个订单，详细信息如下：
模板: {order_data.get('template', '未指定')}
客户编码: {order_data.get('customer_code', '未指定')}
商品列表: {order_data.get('goods_list', [])}
附加信息: {order_data.get('additional_info', {})}

请按照以下流程处理：
1. 解析模板和数据结构
2. 验证客户和商品信息
3. 创建实际订单"""
            
            # 发送开始处理状态
            yield {
                "agent_name": "系统",
                "content": "📦 开始订单创建流程...",
                "status": "processing"
            }
            
            # 使用智能体团队处理订单创建
            async for response in self.process_message(order_message, session_id):
                yield response
                
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            yield {
                "agent_name": "系统",
                "content": f"创建订单时出错: {str(e)}",
                "status": "error"
            }
    
    async def get_team_status(self) -> Dict[str, Any]:
        """获取团队状态"""
        return {
            "initialized": self.team is not None,
            "agents": {
                "template_parser": self.template_parser is not None,
                "info_validator": self.info_validator is not None,
                "order_creator": self.order_creator is not None,
                "coordinator": self.coordinator is not None
            },
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理智能体团队资源...")
        
        # 清理各个智能体
        if self.template_parser:
            await self.template_parser.cleanup()
        
        if self.info_validator:
            await self.info_validator.cleanup()
        
        if self.order_creator:
            await self.order_creator.cleanup()
        
        # 重置引用
        self.template_parser = None
        self.info_validator = None
        self.order_creator = None
        self.coordinator = None
        self.team = None
        
        logger.info("✅ 智能体团队资源清理完成")


class WorkflowOrchestrator:
    """工作流编排器"""
    
    def __init__(self, team_manager: DongCheTeamManager):
        self.team_manager = team_manager
    
    async def execute_template_parsing_workflow(self, template_data: str, session_id: str) -> Dict[str, Any]:
        """执行模板解析工作流"""
        if not self.team_manager.template_parser:
            raise RuntimeError("模板解析智能体未初始化")
        
        return await self.team_manager.template_parser.parse_template(template_data, session_id)
    
    async def execute_validation_workflow(self, data: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """执行信息验证工作流"""
        if not self.team_manager.info_validator:
            raise RuntimeError("信息验证智能体未初始化")
        
        return await self.team_manager.info_validator.validate_info(data, session_id)
    
    async def execute_order_creation_workflow(self, order_data: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """执行订单创建工作流"""
        if not self.team_manager.order_creator:
            raise RuntimeError("订单创建智能体未初始化")
        
        return await self.team_manager.order_creator.create_order(order_data, session_id)
    
    async def execute_full_workflow(self, template_data: str, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """执行完整工作流"""
        try:
            # 步骤1: 模板解析
            yield {"step": "template_parsing", "status": "start", "message": "开始解析模板..."}
            parse_result = await self.execute_template_parsing_workflow(template_data, session_id)
            yield {"step": "template_parsing", "status": "complete", "result": parse_result}
            
            # 步骤2: 信息验证
            yield {"step": "validation", "status": "start", "message": "开始验证信息..."}
            validation_result = await self.execute_validation_workflow(parse_result, session_id)
            yield {"step": "validation", "status": "complete", "result": validation_result}
            
            # 步骤3: 订单创建
            if validation_result.get("valid", False):
                yield {"step": "order_creation", "status": "start", "message": "开始创建订单..."}
                order_result = await self.execute_order_creation_workflow(validation_result, session_id)
                yield {"step": "order_creation", "status": "complete", "result": order_result}
            else:
                yield {"step": "order_creation", "status": "skipped", "message": "信息验证失败，跳过订单创建"}
            
        except Exception as e:
            yield {"step": "error", "status": "failed", "message": str(e)}
