#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存管理器

管理智能体的上下文记忆和会话状态
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import sqlite3
import aiosqlite

logger = logging.getLogger(__name__)


@dataclass
class MemoryItem:
    """记忆项"""
    session_id: str
    timestamp: datetime
    memory_type: str  # message, context, result
    source: str  # user, agent_name, system
    content: str
    metadata: Optional[Dict[str, Any]] = None


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, db_path: str = "memory.db"):
        self.db_path = db_path
        self.session_cache: Dict[str, List[MemoryItem]] = {}
        self.max_cache_size = 1000
        self.cache_ttl = timedelta(hours=24)
        
    async def initialize(self):
        """初始化内存管理器"""
        logger.info("🧠 初始化内存管理器...")
        
        try:
            # 创建数据库表
            await self._create_tables()
            
            # 清理过期记忆
            await self._cleanup_expired_memories()
            
            logger.info("✅ 内存管理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 内存管理器初始化失败: {e}")
            raise
    
    async def _create_tables(self):
        """创建数据库表"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    source TEXT NOT NULL,
                    content TEXT NOT NULL,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_session_id ON memories(session_id)
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_timestamp ON memories(timestamp)
            """)
            
            await db.commit()
    
    async def add_message(self, session_id: str, source: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        """添加消息记忆"""
        memory_item = MemoryItem(
            session_id=session_id,
            timestamp=datetime.now(),
            memory_type="message",
            source=source,
            content=content,
            metadata=metadata
        )
        
        await self._save_memory(memory_item)
        await self._update_cache(memory_item)
    
    async def add_context(self, session_id: str, context_type: str, data: Any, metadata: Optional[Dict[str, Any]] = None):
        """添加上下文记忆"""
        content = json.dumps(data, ensure_ascii=False, default=str)
        
        memory_item = MemoryItem(
            session_id=session_id,
            timestamp=datetime.now(),
            memory_type="context",
            source=context_type,
            content=content,
            metadata=metadata
        )
        
        await self._save_memory(memory_item)
        await self._update_cache(memory_item)
    
    async def add_result(self, session_id: str, agent_name: str, result: Any, metadata: Optional[Dict[str, Any]] = None):
        """添加结果记忆"""
        content = json.dumps(result, ensure_ascii=False, default=str)
        
        memory_item = MemoryItem(
            session_id=session_id,
            timestamp=datetime.now(),
            memory_type="result",
            source=agent_name,
            content=content,
            metadata=metadata
        )
        
        await self._save_memory(memory_item)
        await self._update_cache(memory_item)
    
    async def get_session_memory(self, session_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取会话记忆"""
        # 先从缓存获取
        if session_id in self.session_cache:
            cached_memories = self.session_cache[session_id]
            if len(cached_memories) <= limit:
                return [asdict(item) for item in cached_memories]
        
        # 从数据库获取
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT session_id, timestamp, memory_type, source, content, metadata
                FROM memories
                WHERE session_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (session_id, limit))
            
            rows = await cursor.fetchall()
            
            memories = []
            for row in rows:
                memory_item = MemoryItem(
                    session_id=row[0],
                    timestamp=datetime.fromisoformat(row[1]),
                    memory_type=row[2],
                    source=row[3],
                    content=row[4],
                    metadata=json.loads(row[5]) if row[5] else None
                )
                memories.append(asdict(memory_item))
            
            return memories
    
    async def get_context_memory(self, session_id: str, context_type: str) -> Optional[Dict[str, Any]]:
        """获取特定类型的上下文记忆"""
        memories = await self.get_session_memory(session_id)
        
        for memory in memories:
            if memory["memory_type"] == "context" and memory["source"] == context_type:
                try:
                    return json.loads(memory["content"])
                except json.JSONDecodeError:
                    continue
        
        return None
    
    async def get_recent_messages(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的消息"""
        memories = await self.get_session_memory(session_id, limit * 2)  # 获取更多以过滤
        
        messages = []
        for memory in memories:
            if memory["memory_type"] == "message":
                messages.append(memory)
                if len(messages) >= limit:
                    break
        
        return messages
    
    async def search_memories(self, session_id: str, keyword: str, memory_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索记忆"""
        async with aiosqlite.connect(self.db_path) as db:
            query = """
                SELECT session_id, timestamp, memory_type, source, content, metadata
                FROM memories
                WHERE session_id = ? AND content LIKE ?
            """
            params = [session_id, f"%{keyword}%"]
            
            if memory_type:
                query += " AND memory_type = ?"
                params.append(memory_type)
            
            query += " ORDER BY timestamp DESC LIMIT 50"
            
            cursor = await db.execute(query, params)
            rows = await cursor.fetchall()
            
            memories = []
            for row in rows:
                memory_item = MemoryItem(
                    session_id=row[0],
                    timestamp=datetime.fromisoformat(row[1]),
                    memory_type=row[2],
                    source=row[3],
                    content=row[4],
                    metadata=json.loads(row[5]) if row[5] else None
                )
                memories.append(asdict(memory_item))
            
            return memories
    
    async def clear_session_memory(self, session_id: str):
        """清除会话记忆"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("DELETE FROM memories WHERE session_id = ?", (session_id,))
            await db.commit()
        
        # 清除缓存
        if session_id in self.session_cache:
            del self.session_cache[session_id]
        
        logger.info(f"已清除会话 {session_id} 的记忆")
    
    async def get_memory_summary(self, session_id: str) -> Dict[str, Any]:
        """获取记忆摘要"""
        memories = await self.get_session_memory(session_id)
        
        summary = {
            "session_id": session_id,
            "total_memories": len(memories),
            "memory_types": {},
            "sources": {},
            "time_range": {
                "start": None,
                "end": None
            }
        }
        
        if memories:
            # 统计类型和来源
            for memory in memories:
                memory_type = memory["memory_type"]
                source = memory["source"]
                
                summary["memory_types"][memory_type] = summary["memory_types"].get(memory_type, 0) + 1
                summary["sources"][source] = summary["sources"].get(source, 0) + 1
            
            # 时间范围
            timestamps = [datetime.fromisoformat(m["timestamp"]) for m in memories]
            summary["time_range"]["start"] = min(timestamps).isoformat()
            summary["time_range"]["end"] = max(timestamps).isoformat()
        
        return summary
    
    async def _save_memory(self, memory_item: MemoryItem):
        """保存记忆到数据库"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO memories (session_id, timestamp, memory_type, source, content, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                memory_item.session_id,
                memory_item.timestamp.isoformat(),
                memory_item.memory_type,
                memory_item.source,
                memory_item.content,
                json.dumps(memory_item.metadata) if memory_item.metadata else None
            ))
            await db.commit()
    
    async def _update_cache(self, memory_item: MemoryItem):
        """更新缓存"""
        session_id = memory_item.session_id
        
        if session_id not in self.session_cache:
            self.session_cache[session_id] = []
        
        self.session_cache[session_id].append(memory_item)
        
        # 限制缓存大小
        if len(self.session_cache[session_id]) > self.max_cache_size:
            self.session_cache[session_id] = self.session_cache[session_id][-self.max_cache_size:]
    
    async def _cleanup_expired_memories(self):
        """清理过期记忆"""
        cutoff_time = datetime.now() - timedelta(days=30)  # 保留30天
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                DELETE FROM memories 
                WHERE timestamp < ?
            """, (cutoff_time.isoformat(),))
            await db.commit()
        
        logger.info("已清理过期记忆")
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理内存管理器资源...")
        
        # 清除缓存
        self.session_cache.clear()
        
        logger.info("✅ 内存管理器资源清理完成")
