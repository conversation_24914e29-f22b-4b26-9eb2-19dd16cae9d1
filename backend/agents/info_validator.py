#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信息验证智能体

负责验证客户信息、商品信息等数据的有效性，调用三方API进行验证
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 导入洞车API工具
from backend.qtools import (
    query_daily_goods,
    query_order_goods_list,
    query_driver,
    format_goods_info,
    DongCheAPIError
)

logger = logging.getLogger(__name__)


class InfoValidatorAgent:
    """信息验证智能体"""
    
    def __init__(self, model_client: OpenAIChatCompletionClient, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager
        self.agent: Optional[AssistantAgent] = None
        
    async def initialize(self):
        """初始化智能体"""
        logger.info("🔍 初始化信息验证智能体...")
        
        try:
            # 创建验证工具函数
            async def validate_customer_code(customer_code: str) -> str:
                """验证客户编码"""
                try:
                    # 调用洞车API验证客户
                    result = await query_order_goods_list(customer_code)
                    goods_data = result.get("data", [])
                    
                    if goods_data:
                        return f"✅ 客户编码 {customer_code} 验证通过，该客户有 {len(goods_data)} 个商品"
                    else:
                        return f"⚠️ 客户编码 {customer_code} 存在但暂无商品信息"
                        
                except DongCheAPIError as e:
                    return f"❌ 客户编码 {customer_code} 验证失败: {str(e)}"
                except Exception as e:
                    return f"❌ 验证客户编码时出错: {str(e)}"
            
            async def validate_goods_info(goods_name: str) -> str:
                """验证商品信息"""
                try:
                    # 调用洞车API获取商品列表
                    result = await query_daily_goods()
                    goods_data = result.get("data", [])
                    
                    # 搜索匹配的商品
                    matched_goods = []
                    for goods in goods_data:
                        sku_name = goods.get("skuName", "").lower()
                        brand_name = goods.get("brandName", "").lower()
                        
                        if (goods_name.lower() in sku_name or 
                            sku_name in goods_name.lower() or
                            goods_name.lower() in brand_name):
                            matched_goods.append(goods)
                    
                    if matched_goods:
                        goods_info = format_goods_info(matched_goods[:3])  # 只显示前3个
                        return f"✅ 找到 {len(matched_goods)} 个匹配商品:\n{goods_info}"
                    else:
                        return f"⚠️ 未找到匹配的商品: {goods_name}"
                        
                except DongCheAPIError as e:
                    return f"❌ 商品信息验证失败: {str(e)}"
                except Exception as e:
                    return f"❌ 验证商品信息时出错: {str(e)}"
            
            async def validate_driver_info(driver_name: str) -> str:
                """验证司机信息"""
                try:
                    # 调用洞车API搜索司机
                    result = await query_driver(driver_name)
                    driver_data = result.get("data", [])
                    
                    if driver_data:
                        driver_info = []
                        for driver in driver_data[:3]:  # 只显示前3个
                            info = f"姓名: {driver.get('name', '未知')}"
                            if driver.get('code'):
                                info += f", 编码: {driver['code']}"
                            if driver.get('phoneNumber'):
                                info += f", 电话: {driver['phoneNumber']}"
                            driver_info.append(info)
                        
                        return f"✅ 找到 {len(driver_data)} 个匹配司机:\n" + "\n".join(driver_info)
                    else:
                        return f"⚠️ 未找到匹配的司机: {driver_name}"
                        
                except DongCheAPIError as e:
                    return f"❌ 司机信息验证失败: {str(e)}"
                except Exception as e:
                    return f"❌ 验证司机信息时出错: {str(e)}"
            
            async def validate_data_format(data_str: str) -> str:
                """验证数据格式"""
                try:
                    # 尝试解析JSON
                    if data_str.strip().startswith('{'):
                        json.loads(data_str)
                        return "✅ JSON格式验证通过"
                    else:
                        # 检查基本字段
                        required_keywords = ['客户', '商品', 'customer', 'goods']
                        found_keywords = [kw for kw in required_keywords if kw in data_str.lower()]
                        
                        if found_keywords:
                            return f"✅ 数据格式基本正确，包含关键字: {', '.join(found_keywords)}"
                        else:
                            return "⚠️ 数据格式可能不完整，缺少关键字段"
                            
                except json.JSONDecodeError as e:
                    return f"❌ JSON格式错误: {str(e)}"
                except Exception as e:
                    return f"❌ 数据格式验证出错: {str(e)}"
            
            async def check_data_completeness(data_dict: str) -> str:
                """检查数据完整性"""
                try:
                    if isinstance(data_dict, str):
                        data = json.loads(data_dict)
                    else:
                        data = data_dict
                    
                    required_fields = ['customer_code', 'goods_list']
                    optional_fields = ['order_type', 'delivery_date', 'notes']
                    
                    missing_required = []
                    missing_optional = []
                    
                    for field in required_fields:
                        if field not in data:
                            missing_required.append(field)
                    
                    for field in optional_fields:
                        if field not in data:
                            missing_optional.append(field)
                    
                    result = []
                    if not missing_required:
                        result.append("✅ 必要字段完整")
                    else:
                        result.append(f"❌ 缺少必要字段: {', '.join(missing_required)}")
                    
                    if missing_optional:
                        result.append(f"⚠️ 缺少可选字段: {', '.join(missing_optional)}")
                    
                    return "\n".join(result)
                    
                except Exception as e:
                    return f"❌ 数据完整性检查出错: {str(e)}"
            
            # 创建智能体
            self.agent = AssistantAgent(
                name="信息验证智能体",
                model_client=self.model_client,
                system_message="""你是洞车系统的信息验证专家，负责验证订单相关信息的准确性和有效性。

你的主要职责：
1. 验证客户编码的有效性
2. 验证商品信息是否存在于系统中
3. 验证司机信息的准确性
4. 检查数据格式和完整性
5. 调用洞车API进行实时验证

验证流程：
1. 首先检查数据格式和完整性
2. 验证客户编码是否存在
3. 验证商品信息是否匹配
4. 如果涉及司机，验证司机信息
5. 提供详细的验证报告

输出要求：
- 明确指出验证通过的项目
- 详细说明验证失败的原因
- 提供修正建议
- 给出整体验证结果

请用中文回复，保持专业和准确。""",
                tools=[
                    validate_customer_code,
                    validate_goods_info,
                    validate_driver_info,
                    validate_data_format,
                    check_data_completeness
                ]
            )
            
            logger.info("✅ 信息验证智能体初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 信息验证智能体初始化失败: {e}")
            raise
    
    async def validate_info(self, data: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """验证信息"""
        if not self.agent:
            raise RuntimeError("信息验证智能体未初始化")
        
        try:
            # 保存待验证数据到记忆
            await self.memory_manager.add_context(session_id, "validation_data", data)
            
            # 构建验证请求
            validation_request = f"""请验证以下订单信息：

{json.dumps(data, ensure_ascii=False, indent=2)}

请执行以下验证任务：
1. 检查数据格式和完整性
2. 验证客户编码（如果存在）
3. 验证商品信息（如果存在）
4. 验证司机信息（如果存在）
5. 提供详细的验证报告和建议

请提供完整的验证结果。"""
            
            # 使用智能体验证
            result = await self.agent.run(task=validation_request)
            
            # 处理结果
            if result.messages:
                validation_result = result.messages[-1].content
                
                # 分析验证结果
                validation_summary = await self._analyze_validation_result(validation_result, data)
                
                # 保存验证结果到记忆
                await self.memory_manager.add_result(session_id, "info_validator", {
                    "original_data": data,
                    "validation_result": validation_result,
                    "validation_summary": validation_summary,
                    "timestamp": datetime.now().isoformat()
                })
                
                return {
                    "success": True,
                    "validation_result": validation_result,
                    "validation_summary": validation_summary,
                    "valid": validation_summary.get("overall_valid", False),
                    "agent_name": "信息验证智能体"
                }
            else:
                return {
                    "success": False,
                    "error": "智能体没有返回验证结果",
                    "valid": False,
                    "agent_name": "信息验证智能体"
                }
                
        except Exception as e:
            logger.error(f"信息验证失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "valid": False,
                "agent_name": "信息验证智能体"
            }
    
    async def _analyze_validation_result(self, validation_result: str, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析验证结果"""
        summary = {
            "overall_valid": False,
            "passed_checks": [],
            "failed_checks": [],
            "warnings": [],
            "suggestions": []
        }
        
        try:
            lines = validation_result.split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if '✅' in line:
                    summary["passed_checks"].append(line.replace('✅', '').strip())
                elif '❌' in line:
                    summary["failed_checks"].append(line.replace('❌', '').strip())
                elif '⚠️' in line:
                    summary["warnings"].append(line.replace('⚠️', '').strip())
            
            # 判断整体是否有效
            if summary["failed_checks"]:
                summary["overall_valid"] = False
                summary["suggestions"].append("请修复所有验证失败的项目")
            elif summary["warnings"]:
                summary["overall_valid"] = True  # 有警告但可以继续
                summary["suggestions"].append("建议完善警告提到的信息")
            else:
                summary["overall_valid"] = True
                summary["suggestions"].append("所有验证通过，可以继续创建订单")
            
            # 添加具体建议
            if 'customer_code' in original_data and not any('客户编码' in check for check in summary["passed_checks"]):
                summary["suggestions"].append("建议验证客户编码的有效性")
            
            if 'goods_list' in original_data and not any('商品' in check for check in summary["passed_checks"]):
                summary["suggestions"].append("建议验证商品信息的准确性")
            
        except Exception as e:
            logger.warning(f"验证结果分析失败: {e}")
            summary["failed_checks"].append(f"验证结果分析出错: {str(e)}")
        
        return summary
    
    async def quick_validate(self, field_name: str, field_value: str) -> Dict[str, Any]:
        """快速验证单个字段"""
        try:
            if field_name.lower() in ['customer_code', '客户编码']:
                result = await query_order_goods_list(field_value)
                return {
                    "valid": bool(result.get("data")),
                    "message": f"客户编码 {field_value} {'有效' if result.get('data') else '无效'}"
                }
            elif field_name.lower() in ['goods_name', '商品名称']:
                result = await query_daily_goods()
                goods_data = result.get("data", [])
                matched = any(field_value.lower() in goods.get("skuName", "").lower() for goods in goods_data)
                return {
                    "valid": matched,
                    "message": f"商品 {field_value} {'找到匹配' if matched else '未找到匹配'}"
                }
            else:
                return {
                    "valid": True,
                    "message": f"字段 {field_name} 格式正确"
                }
        except Exception as e:
            return {
                "valid": False,
                "message": f"验证 {field_name} 时出错: {str(e)}"
            }
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理信息验证智能体资源...")
        self.agent = None
        logger.info("✅ 信息验证智能体资源清理完成")
