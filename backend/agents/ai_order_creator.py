#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI造数工具 - 智能体系统

基于AutoGen框架的智能订单创建系统
"""

import json
import re
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# AutoGen框架 - 如果不可用则使用简化实现
try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.teams import RoundRobinGroupChat
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    # 简化的智能体实现
    class AssistantAgent:
        def __init__(self, name, model_client, system_message, tools=None):
            self.name = name
            self.model_client = model_client
            self.system_message = system_message
            self.tools = tools or []

        async def run(self, task):
            """简化的运行方法"""
            class SimpleResult:
                def __init__(self, content):
                    self.messages = [SimpleMessage(content)]

            class SimpleMessage:
                def __init__(self, content):
                    self.content = content

            # 模拟智能体响应
            return SimpleResult(f"✅ {self.name}处理完成: {task[:50]}...")

# 导入洞车API工具
try:
    from qtools import (
        query_customer, query_shop, query_order_goods_list,
        query_goods_sku_list, create_order_rk, get_token,
        DongCheAPIError
    )
    TOOLS_AVAILABLE = True
except ImportError:
    TOOLS_AVAILABLE = False

    # 如果qtools不可用，创建占位符函数
    class DongCheAPIError(Exception):
        pass

    async def query_customer(customer_name=""):
        return {"data": [{"customerCode": "auto001", "customerName": "自动化商户"}]}

    async def query_shop(customer_code=""):
        return {"data": [{"shopCode": "auto001", "shopName": "自动化店铺"}]}

    async def query_order_goods_list(customer_code=""):
        return {"data": [{"skuName": "自动化商品", "specifications": "自动化规格", "customerSkuCode": "AUTO001"}]}

    async def query_goods_sku_list(commodity_id=""):
        return {"data": []}

    async def create_order_rk(num=1, order_data=None):
        return {"code": "200", "data": [{"index": "生成上架单", "workOrderTypeName": "采购入库"}]}

    async def get_token():
        return {"token": "demo_token"}

logger = logging.getLogger(__name__)


class IntentParserAgent:
    """意图解析智能体 - 解析用户自然语言需求"""

    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager

    async def initialize(self):
        """初始化智能体"""
        logger.info("🧠 初始化意图解析智能体...")
        logger.info("✅ 意图解析智能体初始化完成")

    async def parse_intent(self, user_input: str, session_id: str) -> Dict[str, Any]:
        """解析用户意图 - 使用智能正则和NLP技术"""
        try:
            logger.info(f"🧠 开始解析用户意图: {user_input}")

            # 智能提取数量
            quantity_patterns = [
                r'(\d+)个',
                r'(\d+)张',
                r'(\d+)份',
                r'创建(\d+)',
                r'生成(\d+)',
                r'(\d+)单'
            ]
            quantity = 1
            for pattern in quantity_patterns:
                match = re.search(pattern, user_input)
                if match:
                    quantity = int(match.group(1))
                    break

            # 智能识别订单类型
            order_type = "入库单"
            if any(keyword in user_input for keyword in ["出库", "出仓", "发货", "配送"]):
                order_type = "出库单"
            elif any(keyword in user_input for keyword in ["入库", "入仓", "收货", "到货"]):
                order_type = "入库单"

            # 智能提取商户信息
            customer_patterns = [
                r'商户[：:]\s*([^，,\s]+)',
                r'客户[：:]\s*([^，,\s]+)',
                r'商家[：:]\s*([^，,\s]+)',
                r'公司[：:]\s*([^，,\s]+)'
            ]
            customer = ""
            for pattern in customer_patterns:
                match = re.search(pattern, user_input)
                if match:
                    customer = match.group(1).strip()
                    break

            # 智能提取店铺信息
            shop_patterns = [
                r'店铺[：:]\s*([^，,\s]+)',
                r'门店[：:]\s*([^，,\s]+)',
                r'店面[：:]\s*([^，,\s]+)',
                r'分店[：:]\s*([^，,\s]+)'
            ]
            shop = ""
            for pattern in shop_patterns:
                match = re.search(pattern, user_input)
                if match:
                    shop = match.group(1).strip()
                    break

            # 智能提取订单状态
            status_patterns = [
                r'状态[：:]\s*([^，,\s]+)',
                r'订单状态[：:]\s*([^，,\s]+)',
                r'工单状态[：:]\s*([^，,\s]+)'
            ]
            status = ""
            for pattern in status_patterns:
                match = re.search(pattern, user_input)
                if match:
                    status = match.group(1).strip()
                    break

            # 智能识别是否提货
            pickup = False
            pickup_keywords = ["提货", "自提", "取货", "pickup", "需要提货", "要提货"]
            no_pickup_keywords = ["不提货", "不需要提货", "无需提货"]

            # 先检查否定词
            if any(keyword in user_input for keyword in no_pickup_keywords):
                pickup = False
            # 再检查肯定词
            elif any(keyword in user_input for keyword in pickup_keywords):
                pickup = True
            else:
                # 默认根据订单类型判断，入库单通常需要提货
                pickup = True if order_type == "入库单" else False

            logger.info(f"🚚 提货识别: {pickup} (关键词检查: {user_input})")

            # 如果没有明确状态，根据关键词推断
            if not status:
                if "收货" in user_input:
                    status = "收货"
                elif "生成上架单" in user_input:
                    status = "生成上架单"
                elif "上架" in user_input:
                    status = "上架"
                elif "合单" in user_input:
                    status = "合单"
                elif "派单" in user_input:
                    status = "派单"
                elif "发车" in user_input:
                    status = "发车"
                elif "签收" in user_input:
                    status = "签收"

            # 根据订单状态强制设置pickup值
            delivery_statuses = ["合单", "派单", "发车", "签收"]
            if status in delivery_statuses:
                pickup = True
                logger.info(f"🚚 订单状态 '{status}' 强制设置 pickup=True")

            # 构建解析结果
            parsed_result = {
                "quantity": quantity,
                "order_type": order_type,
                "customer": customer,
                "shop": shop,
                "status": status,
                "pickup": pickup,
                "original_input": user_input,
                "confidence": self._calculate_confidence(user_input, quantity, customer, shop)
            }

            # 保存到记忆
            await self.memory_manager.add_context(
                session_id,
                "intent_parsing",
                parsed_result,
                {"agent": "IntentParserAgent", "timestamp": datetime.now().isoformat()}
            )

            logger.info(f"✅ 意图解析完成: {parsed_result}")
            return parsed_result

        except Exception as e:
            logger.error(f"❌ 意图解析失败: {e}")
            return {
                "error": str(e),
                "quantity": 1,
                "order_type": "入库单",
                "customer": "",
                "shop": "",
                "status": "",
                "pickup": False,
                "original_input": user_input,
                "confidence": 0.0
            }

    def _calculate_confidence(self, user_input: str, quantity: int, customer: str, shop: str) -> float:
        """计算解析置信度"""
        confidence = 0.0

        # 基础分数
        if quantity > 0:
            confidence += 0.3
        if customer:
            confidence += 0.3
        if shop:
            confidence += 0.2

        # 关键词匹配度
        keywords = ["创建", "生成", "帮我", "订单", "入库", "出库"]
        matched_keywords = sum(1 for keyword in keywords if keyword in user_input)
        confidence += (matched_keywords / len(keywords)) * 0.2

        return min(confidence, 1.0)


class DataQueryAgent:
    """数据查询智能体 - 智能查询基础数据"""

    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager

    async def initialize(self):
        """初始化智能体"""
        logger.info("🔍 初始化数据查询智能体...")
        logger.info("✅ 数据查询智能体初始化完成")

    async def query_base_data(self, intent_data: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """智能查询基础数据"""
        try:
            customer_name = intent_data.get("customer", "")
            shop_name = intent_data.get("shop", "")

            logger.info(f"🔍 开始查询基础数据 - 商户: {customer_name}, 店铺: {shop_name}")

            # 步骤1: 智能查询商户信息
            logger.info("📋 查询商户信息...")
            customer_data = await self._smart_query_customer(customer_name)

            if "error" in customer_data:
                return customer_data

            # 如果需要使用默认商户
            if customer_data.get("use_default"):
                logger.info("🔧 直接使用默认商户信息，跳过店铺和商品查询")
                return self._build_default_order_data(intent_data)

            selected_customer = customer_data["customer"]
            customer_code = selected_customer.get("customerCode", "")

            # 步骤2: 查询店铺信息
            logger.info(f"🏪 查询店铺信息 - 商户编码: {customer_code}")
            shop_data = await self._smart_query_shops(customer_code, shop_name)

            # 验证店铺数据
            if not shop_data.get("selected_shop"):
                logger.warning(f"⚠️ 商户 {customer_code} 没有可用店铺")

            # 步骤3: 查询商品信息
            logger.info(f"📦 查询商品信息 - 商户编码: {customer_code}")
            goods_data = await self._smart_query_goods(customer_code)

            # 验证商品数据
            if not goods_data.get("selected_goods"):
                logger.warning(f"⚠️ 商户 {customer_code} 没有可用商品")

                # 如果需要更换店铺
                if goods_data.get("need_change_shop"):
                    logger.info("🔄 尝试更换店铺...")
                    shop_data = await self._try_other_shops(customer_code, shop_name)

                    if shop_data.get("selected_shop"):
                        # 重新查询新店铺的商品
                        goods_data = await self._smart_query_goods(customer_code)

                    # 如果还是没有有效商品，需要更换商户
                    if not goods_data.get("selected_goods"):
                        logger.warning("🔄 需要更换商户...")
                        return {"error": "need_change_customer", "message": "当前商户及其店铺都没有有效商品"}

            # 构建完整结果
            result = {
                "customer": selected_customer,
                "shops": shop_data.get("shops", []),
                "goods": goods_data.get("goods", []),
                "customer_code": customer_code,
                "selected_shop": shop_data.get("selected_shop"),
                "selected_goods": goods_data.get("selected_goods", []),
                "query_summary": {
                    "customer_found": True,
                    "shops_count": len(shop_data.get("shops", [])),
                    "goods_count": len(goods_data.get("goods", [])),
                    "confidence": customer_data.get("confidence", 0.8)
                }
            }

            # 保存到记忆
            await self.memory_manager.add_context(
                session_id,
                "data_query",
                result,
                {"agent": "DataQueryAgent", "timestamp": datetime.now().isoformat()}
            )

            logger.info(f"✅ 基础数据查询完成: 商户={selected_customer.get('customerName')}, 店铺数={len(shop_data.get('shops', []))}, 商品数={len(goods_data.get('goods', []))}")
            return result

        except Exception as e:
            logger.error(f"❌ 数据查询失败: {e}")
            return {"error": str(e)}

    async def _smart_query_customer(self, customer_name: str) -> Dict[str, Any]:
        """智能查询商户信息"""
        try:
            # 如果没有提供商户名，使用默认值
            if not customer_name:
                customer_name = "自动化"

            # 查询商户
            result = await query_customer(customer_name)
            customers = result.get("data", [])

            if not customers:
                logger.warning(f"⚠️ 未找到商户 '{customer_name}'，直接使用默认商户信息")
                return {"use_default": True, "message": "使用默认商户信息"}

            # 选择有店铺和有效商品的最佳商户
            selected_customer = await self._select_best_customer_with_data(customers, customer_name)

            logger.info(f"✅ 商户查询完成 - 选中: {selected_customer.get('customerName', '未知')}, 总数: {len(customers)}")

            return {
                "customer": selected_customer,
                "all_customers": customers,
                "confidence": self._calculate_customer_confidence(selected_customer, customer_name)
            }

        except Exception as e:
            logger.error(f"查询商户失败: {e}")
            return {"error": f"查询商户失败: {str(e)}"}

    async def _smart_query_shops(self, customer_code: str, shop_name: str = "") -> Dict[str, Any]:
        """智能查询店铺信息"""
        try:
            result = await query_shop(customer_code)
            shops = result.get("data", [])

            if not shops:
                logger.info(f"🔍 商户 {customer_code} 下没有店铺，尝试查询所有店铺...")
                # 尝试查询其他商户的店铺作为备选
                try:
                    # 查询所有商户
                    all_customers_result = await query_customer("")
                    all_customers = all_customers_result.get("data", [])

                    all_shops = []
                    for customer in all_customers[:3]:  # 只查询前3个商户的店铺
                        customer_code_temp = customer.get("customerCode", "")
                        if customer_code_temp and customer_code_temp != customer_code:
                            try:
                                shop_result = await query_shop(customer_code_temp)
                                customer_shops = shop_result.get("data", [])
                                all_shops.extend(customer_shops)
                            except:
                                continue

                    if all_shops:
                        shops = all_shops
                        logger.info(f"📋 找到 {len(all_shops)} 个其他商户的店铺供选择")
                    else:
                        return {"shops": [], "selected_shop": None}
                except:
                    return {"shops": [], "selected_shop": None}

            # 如果指定了店铺名，尝试匹配
            selected_shop = None
            if shop_name:
                for shop in shops:
                    shop_full_name = shop.get("shopName", "")
                    if shop_name.lower() in shop_full_name.lower() or shop_full_name.lower() in shop_name.lower():
                        selected_shop = shop
                        break

            # 如果没有找到匹配的店铺，选择第一个
            if not selected_shop and shops:
                selected_shop = shops[0]

            logger.info(f"✅ 店铺查询完成 - 选中: {selected_shop.get('shopName', '未知') if selected_shop else '无'}, 总数: {len(shops)}")

            return {
                "shops": shops,
                "selected_shop": selected_shop
            }

        except Exception as e:
            logger.error(f"查询店铺失败: {e}")
            return {"shops": [], "selected_shop": None, "error": str(e)}

    async def _smart_query_goods(self, customer_code: str) -> Dict[str, Any]:
        """智能查询商品信息"""
        try:
            result = await query_order_goods_list(customer_code)
            goods = result.get("data", [])

            if not goods:
                logger.info(f"🔍 商户 {customer_code} 下没有商品，尝试查询其他商户的商品...")
                # 尝试查询其他商户的商品
                try:
                    # 查询所有商户
                    all_customers_result = await query_customer("")
                    all_customers = all_customers_result.get("data", [])

                    all_goods = []
                    for customer in all_customers[:3]:  # 只查询前3个商户的商品
                        customer_code_temp = customer.get("customerCode", "")
                        if customer_code_temp and customer_code_temp != customer_code:
                            try:
                                goods_result = await query_order_goods_list(customer_code_temp)
                                customer_goods = goods_result.get("data", [])
                                all_goods.extend(customer_goods[:2])  # 每个商户最多取2个商品
                            except:
                                continue

                    if all_goods:
                        goods = all_goods
                        logger.info(f"📦 找到 {len(all_goods)} 个其他商户的商品供选择")
                    else:
                        # 如果还是没有商品，使用默认商品
                        default_goods = self._create_default_goods()
                        return {"goods": [default_goods], "selected_goods": [default_goods]}
                except:
                    # 如果查询失败，使用默认商品
                    default_goods = self._create_default_goods()
                    return {"goods": [default_goods], "selected_goods": [default_goods]}

            # 选择有有效批次号的前2个商品
            selected_goods = await self._select_goods_with_valid_batch(goods[:5])  # 从前5个中选择

            if not selected_goods:
                logger.warning(f"⚠️ 商户 {customer_code} 的商品都没有有效批次号")
                # 如果没有有效商品，返回空，触发更换逻辑
                return {"goods": goods, "selected_goods": [], "need_change_shop": True}

            return {
                "goods": goods,
                "selected_goods": selected_goods
            }

        except Exception as e:
            logger.error(f"查询商品失败: {e}")
            # 返回默认商品
            default_goods = self._create_default_goods()
            return {"goods": [default_goods], "selected_goods": [default_goods]}

    async def _select_best_customer_with_data(self, customers: List[Dict], target_name: str) -> Dict[str, Any]:
        """选择有店铺和有效商品的最佳商户"""
        if not customers:
            return {}

        # 如果只有一个商户，检查是否有有效数据
        if len(customers) == 1:
            customer = customers[0]
            has_valid_data = await self._check_customer_has_valid_batch_data(customer)
            if has_valid_data:
                return customer
            else:
                logger.warning(f"⚠️ 商户 {customer.get('customerName')} 没有有效的商品批次数据")
                return customer  # 即使没有有效数据也返回，后续会处理

        # 多个商户时，计算每个商户的有效数据评分
        customer_scores = []
        for customer in customers:
            score = await self._calculate_customer_valid_data_score(customer, target_name)
            customer_scores.append((customer, score))
            logger.info(f"📊 商户 {customer.get('customerName')} 有效数据评分: {score}")

        # 按评分排序，选择最高分的商户
        customer_scores.sort(key=lambda x: x[1], reverse=True)
        best_customer = customer_scores[0][0]

        logger.info(f"🏆 选择最佳商户: {best_customer.get('customerName')} (有效数据评分: {customer_scores[0][1]})")
        return best_customer

    async def _check_customer_has_data(self, customer: Dict[str, Any]) -> bool:
        """检查商户是否有店铺和商品数据"""
        try:
            customer_code = customer.get("customerCode", "")
            if not customer_code:
                return False

            # 检查店铺
            shop_result = await query_shop(customer_code)
            shops = shop_result.get("data", [])

            # 检查商品
            goods_result = await query_order_goods_list(customer_code)
            goods = goods_result.get("data", [])

            has_shops = len(shops) > 0
            has_goods = len(goods) > 0

            logger.info(f"📊 商户 {customer.get('customerName')} - 店铺: {len(shops)}个, 商品: {len(goods)}个")

            return has_shops and has_goods

        except Exception as e:
            logger.error(f"检查商户数据失败: {e}")
            return False

    async def _calculate_customer_data_score(self, customer: Dict[str, Any], target_name: str) -> float:
        """计算商户数据完整性评分"""
        score = 0.0

        try:
            customer_code = customer.get("customerCode", "")
            customer_name = customer.get("customerName", "")

            # 基础分数
            if customer_code:
                score += 10
            if customer_name:
                score += 10

            # 名称匹配分数
            if target_name and customer_name:
                if target_name.lower() in customer_name.lower():
                    score += 30
                elif customer_name.lower() in target_name.lower():
                    score += 20

            # 店铺数据分数
            try:
                shop_result = await query_shop(customer_code)
                shops = shop_result.get("data", [])
                if shops:
                    score += 25  # 有店铺
                    score += min(len(shops) * 2, 10)  # 店铺数量加分，最多10分
            except:
                pass

            # 商品数据分数
            try:
                goods_result = await query_order_goods_list(customer_code)
                goods = goods_result.get("data", [])
                if goods:
                    score += 25  # 有商品
                    score += min(len(goods) * 1, 15)  # 商品数量加分，最多15分
            except:
                pass

            return score

        except Exception as e:
            logger.error(f"计算商户评分失败: {e}")
            return 0.0

    def _select_best_customer(self, customers: List[Dict], target_name: str) -> Dict[str, Any]:
        """选择最佳匹配的商户"""
        if not customers:
            return {}

        # 如果只有一个，直接返回
        if len(customers) == 1:
            return customers[0]

        # 计算匹配度
        best_customer = customers[0]
        best_score = 0

        for customer in customers:
            customer_name = customer.get("customerName", "")
            score = self._calculate_name_similarity(customer_name, target_name)
            if score > best_score:
                best_score = score
                best_customer = customer

        return best_customer

    def _calculate_customer_confidence(self, customer: Dict[str, Any], target_name: str) -> float:
        """计算商户匹配置信度"""
        if not customer:
            return 0.0

        customer_name = customer.get("customerName", "")
        return self._calculate_name_similarity(customer_name, target_name)

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算名称相似度"""
        if not name1 or not name2:
            return 0.0

        name1_lower = name1.lower()
        name2_lower = name2.lower()

        # 完全匹配
        if name1_lower == name2_lower:
            return 1.0

        # 包含匹配
        if name2_lower in name1_lower or name1_lower in name2_lower:
            return 0.8

        # 字符匹配度
        common_chars = set(name1_lower) & set(name2_lower)
        total_chars = set(name1_lower) | set(name2_lower)

        if total_chars:
            return len(common_chars) / len(total_chars)

        return 0.0

    def _create_default_goods(self) -> Dict[str, Any]:
        """创建默认商品信息"""
        return {
            "customerSkuCode": "DEFAULT_SKU",
            "brandName": "默认品牌",
            "skuBrand": "0000001",
            "firstCategoryName": "默认分类",
            "firstCategoryLevel": "001",
            "iotSkuCode": "default_iot_001",
            "skuName": "默认商品",
            "specifications": "默认规格",
            "volume": "1.0",
            "grossWeight": "1.0",
            "length": 100,
            "width": 100,
            "height": 100,
            "id": "default_001"
        }

    async def _check_customer_has_valid_batch_data(self, customer: Dict[str, Any]) -> bool:
        """检查商户是否有有效的商品批次数据"""
        try:
            customer_code = customer.get("customerCode", "")
            if not customer_code:
                return False

            # 检查商品并验证批次号
            goods_result = await query_order_goods_list(customer_code)
            goods = goods_result.get("data", [])

            if not goods:
                return False

            # 检查是否有有效批次号的商品
            valid_goods = await self._select_goods_with_valid_batch(goods[:5])
            has_valid_goods = len(valid_goods) > 0

            logger.info(f"📊 商户 {customer.get('customerName')} - 有效商品: {len(valid_goods)}个")

            return has_valid_goods

        except Exception as e:
            logger.error(f"检查商户有效数据失败: {e}")
            return False

    async def _calculate_customer_valid_data_score(self, customer: Dict[str, Any], target_name: str) -> float:
        """计算商户有效数据评分（包含批次号验证）"""
        score = 0.0

        try:
            customer_code = customer.get("customerCode", "")
            customer_name = customer.get("customerName", "")

            # 基础分数
            if customer_code:
                score += 10
            if customer_name:
                score += 10

            # 名称匹配分数
            if target_name and customer_name:
                if target_name.lower() in customer_name.lower():
                    score += 30
                elif customer_name.lower() in target_name.lower():
                    score += 20

            # 店铺数据分数
            try:
                shop_result = await query_shop(customer_code)
                shops = shop_result.get("data", [])
                if shops:
                    score += 20  # 有店铺
            except:
                pass

            # 有效商品数据分数（重点）
            try:
                goods_result = await query_order_goods_list(customer_code)
                goods = goods_result.get("data", [])
                if goods:
                    valid_goods = await self._select_goods_with_valid_batch(goods[:5])
                    if valid_goods:
                        score += 50  # 有有效商品批次号（高分）
                        score += min(len(valid_goods) * 10, 30)  # 有效商品数量加分
                    else:
                        score += 10  # 有商品但没有有效批次号（低分）
            except:
                pass

            return score

        except Exception as e:
            logger.error(f"计算商户有效数据评分失败: {e}")
            return 0.0

    def _build_default_order_data(self, intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建默认订单数据 - 直接返回完整的默认数据结构"""
        logger.info("🔧 构建默认订单数据，跳过所有API查询")

        # 直接返回默认数据结构，标记为使用默认订单
        return {
            "customer": {
                "customerCode": "DCKH0028",
                "customerName": "自动化商户"
            },
            "shops": [{"shopCode": "1901485162790879233", "shopName": "自动化店铺"}],
            "selected_shop": {"shopCode": "1901485162790879233", "shopName": "自动化店铺"},
            "goods": [
                {
                    "customerSkuCode": "自动化标品************",
                    "skuName": "自动化标品************",
                    "id": "1950254997911306242"
                },
                {
                    "customerSkuCode": "自动化定制************",
                    "skuName": "自动化定制************",
                    "id": "1950255025274945538"
                }
            ],
            "selected_goods": [
                {
                    "customerSkuCode": "自动化标品************",
                    "brandName": "自动化",
                    "skuBrand": "DCPP0219",
                    "firstCategoryName": "卫浴",
                    "firstCategoryLevel": "073",
                    "iotSkuCode": "SP730219000350",
                    "skuName": "自动化标品************",
                    "specifications": "100*100*100",
                    "volume": "0.001000000",
                    "grossWeight": "10.00",
                    "length": 100,
                    "width": 100,
                    "height": 100,
                    "id": "1950254997911306242",
                    "batchNumber": 1,
                    "customizedProduct": 0
                },
                {
                    "customerSkuCode": "自动化定制************",
                    "brandName": "自动化",
                    "skuBrand": "DCPP0219",
                    "firstCategoryName": "卫浴",
                    "firstCategoryLevel": "073",
                    "iotSkuCode": "SP730219000351",
                    "skuName": "自动化定制************",
                    "specifications": "100*100*100",
                    "volume": "0.001000000",
                    "grossWeight": "10.00",
                    "length": 100,
                    "width": 100,
                    "height": 100,
                    "id": "1950255025274945538",
                    "batchNumber": 1,
                    "customizedProduct": 1
                }
            ],
            "is_default": True
        }


class ParameterBuilderAgent:
    """参数构建智能体 - 智能构建订单参数"""

    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager

    async def initialize(self):
        """初始化智能体"""
        logger.info("🔧 初始化参数构建智能体...")
        logger.info("✅ 参数构建智能体初始化完成")

    async def build_parameters(self, intent_data: Dict[str, Any], base_data: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """智能构建订单参数"""
        try:
            logger.info("🔧 开始构建订单参数...")

            # 检查是否使用默认订单
            if base_data.get("is_default"):
                logger.info("🔧 使用默认订单参数 - 跳过所有数据查询")
                logger.info("📋 默认商户: DCKH0028 自动化商户")
                logger.info("🏪 默认店铺: 1901485162790879233 自动化店铺")
                logger.info("📦 默认商品: 自动化标品 + 自动化定制")

                default_params = self._build_default_order_params(intent_data)

                # 保存到记忆
                await self.memory_manager.add_context(
                    session_id,
                    "default_parameter_building",
                    default_params,
                    {"agent": "ParameterBuilderAgent", "timestamp": datetime.now().isoformat(), "type": "default"}
                )

                logger.info("✅ 默认订单参数构建完成，无需API查询")

                # 调试：输出完整的JSON结构
                import json
                logger.info(f"🔍 默认订单JSON: {json.dumps(default_params, ensure_ascii=False, indent=2)}")

                # 特别检查关键字段
                first_goods = default_params.get("orderGoodsList", [{}])[0].get("goodsDetail", {})
                logger.info(f"🔍 第一个商品关键字段:")
                logger.info(f"   iotSkuCode: {first_goods.get('iotSkuCode')}")
                logger.info(f"   id: {first_goods.get('id')}")
                logger.info(f"   batchNumber: {first_goods.get('batchNumber')} (类型: {type(first_goods.get('batchNumber'))})")

                return default_params

            # 提取基础数据
            customer = base_data.get("customer", {})
            selected_shop = base_data.get("selected_shop")
            selected_goods = base_data.get("selected_goods", [])

            # 验证必要数据
            if not customer:
                return {"error": "缺少商户信息"}

            if not selected_shop:
                shops = base_data.get("shops", [])
                selected_shop = shops[0] if shops else {}

            if not selected_goods:
                goods = base_data.get("goods", [])
                selected_goods = goods[:2] if goods else [self._create_default_goods()]  # 选择前2个商品

            logger.info(f"📦 最终选择的商品数量: {len(selected_goods)}")

            # 构建订单参数
            order_params = await self._build_order_template(
                intent_data, customer, selected_shop, selected_goods
            )

            # 根据订单状态设置工单流程
            order_params["wordOrderReqList"] = self._build_workflow(intent_data)

            # 保存到记忆
            await self.memory_manager.add_context(
                session_id,
                "parameter_building",
                order_params,
                {"agent": "ParameterBuilderAgent", "timestamp": datetime.now().isoformat()}
            )

            logger.info(f"✅ 订单参数构建完成 - 商户: {customer.get('customerName')}, 店铺: {selected_shop.get('shopName')}")
            return order_params

        except Exception as e:
            logger.error(f"❌ 参数构建失败: {e}")
            return {"error": str(e)}

    async def _build_order_template(self, intent_data: Dict[str, Any], customer: Dict[str, Any],
                            shop: Dict[str, Any], selected_goods: List[Dict[str, Any]]) -> Dict[str, Any]:
        """构建订单模板 - 使用真实商品数据"""

        # 确定是否提货
        pickup = intent_data.get("pickup", False)

        # 构建商品列表 - 使用真实查询到的商品数据
        logger.info(f"📦 构建商品列表 - 商品数量: {len(selected_goods)}")
        if selected_goods:
            logger.info(f"📦 第一个商品示例: {selected_goods[0]}")

        order_goods_list = await self._build_goods_list_from_real_data(selected_goods)

        # 构建订单参数
        order_params = {
            "associatedOrderNo": "",
            "customer": {
                "customerCode": customer.get("customerCode", ""),
                "customerName": customer.get("customerName", ""),
                "shopCode": shop.get("shopCode", ""),
                "shopName": shop.get("shopName", "")
            },
            "index": "",
            "orderGoodsList": order_goods_list,
            "pickup": pickup,
            "type": 0,  # 入库单类型 (根据示例应该是0)
            "wordOrderReqList": []  # 将在后面设置
        }

        return order_params

    async def _build_goods_list_from_real_data(self, selected_goods: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据真实商品数据构建订单商品列表"""
        if not selected_goods:
            # 如果没有真实商品数据，使用默认商品
            return self._build_default_goods_list()

        order_goods_list = []
        for goods in selected_goods:
            # 获取商品的SKU信息来获取正确的batchNumber
            goods_id = goods.get("id", "")
            batch_number = await self._get_goods_batch_number(goods_id)

            # 直接使用真实商品数据，完全保持原有字段
            goods_item = {
                "goodsDetail": {
                    "customerSkuCode": goods.get("customerSkuCode", ""),
                    "brandName": goods.get("brandName", ""),
                    "skuBrand": goods.get("skuBrand", ""),
                    "firstCategoryName": goods.get("firstCategoryName", ""),
                    "firstCategoryLevel": goods.get("firstCategoryLevel", ""),
                    "iotSkuCode": goods.get("iotSkuCode", ""),  # 保持原始值
                    "skuName": goods.get("skuName", ""),
                    "specifications": goods.get("specifications", ""),
                    "volume": str(goods.get("volume", "0")),
                    "grossWeight": str(goods.get("grossWeight", "0")),
                    "length": int(goods.get("length", 0)) if goods.get("length") else 0,
                    "width": int(goods.get("width", 0)) if goods.get("width") else 0,
                    "height": int(goods.get("height", 0)) if goods.get("height") else 0,
                    "id": str(goods.get("id", "")),  # 保持原始值
                    "batchNumber": batch_number,  # 使用从SKU API获取的真实批次号
                    "customizedProduct": int(goods.get("customizedProduct", 0)),
                    "installed": None,
                    "orderNo": None,
                    "packageQuantity": None,
                    "quantity": "1",
                    "skuStatus": None,
                    "skuType": None,
                    "mdmSkuId": None,
                    "belongRelatedNo": None,
                    "serialNumber": None,
                    "secondCategoryLevel": None,
                    "thirdCategoryLevel": None,
                    "brandCode": None,
                    "specialType": None,
                    "relOrderNo": None,
                    "asnNo": "",
                    "lineNum": ""
                },
                "warehouse": 0
            }
            order_goods_list.append(goods_item)

        return order_goods_list

    async def _get_goods_batch_number(self, goods_id: str) -> str:
        """通过商品ID获取真实的批次号"""
        try:
            if not goods_id:
                return ""

            # 调用SKU列表API
            sku_result = await query_goods_sku_list(goods_id)
            sku_list = sku_result.get("data", [])

            if sku_list:
                # 选择第一个SKU的批次号
                first_sku = sku_list[0]
                batch_number = first_sku.get("lotNum", "")
                logger.info(f"📦 商品 {goods_id} 获取批次号: {batch_number}")
                return batch_number
            else:
                logger.warning(f"⚠️ 商品 {goods_id} 没有SKU数据")
                return ""

        except Exception as e:
            logger.error(f"❌ 获取商品 {goods_id} 批次号失败: {e}")
            return ""

    async def _select_goods_with_valid_batch(self, goods_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择有有效批次号的商品"""
        valid_goods = []

        for goods in goods_list:
            goods_id = goods.get("id", "")
            batch_number = await self._get_goods_batch_number(goods_id)

            if batch_number:  # 有有效批次号
                logger.info(f"✅ 商品 {goods.get('skuName', '')} (ID: {goods_id}) 有有效批次号: {batch_number}")
                valid_goods.append(goods)

                # 找到2个有效商品就够了
                if len(valid_goods) >= 2:
                    break
            else:
                logger.warning(f"⚠️ 商品 {goods.get('skuName', '')} (ID: {goods_id}) 没有有效批次号，跳过")

        return valid_goods

    async def _try_other_shops(self, customer_code: str, shop_name: str = "") -> Dict[str, Any]:
        """尝试其他店铺"""
        try:
            result = await query_shop(customer_code)
            shops = result.get("data", [])

            if len(shops) <= 1:
                logger.warning(f"⚠️ 商户 {customer_code} 只有一个或没有店铺")
                return {"shops": shops, "selected_shop": None}

            # 尝试其他店铺
            for shop in shops[1:]:  # 跳过第一个店铺
                shop_code = shop.get("shopCode", "")
                logger.info(f"🔄 尝试店铺: {shop.get('shopName', '')} (Code: {shop_code})")

                # 检查这个店铺是否有有效商品
                goods_result = await query_order_goods_list(customer_code)
                goods = goods_result.get("data", [])

                if goods:
                    valid_goods = await self._select_goods_with_valid_batch(goods[:5])
                    if valid_goods:
                        logger.info(f"✅ 店铺 {shop.get('shopName', '')} 有有效商品")
                        return {"shops": shops, "selected_shop": shop}

            logger.warning(f"⚠️ 商户 {customer_code} 的所有店铺都没有有效商品")
            return {"shops": shops, "selected_shop": None}

        except Exception as e:
            logger.error(f"尝试其他店铺失败: {e}")
            return {"shops": [], "selected_shop": None}



    def _build_default_goods_list(self) -> List[Dict[str, Any]]:
        """构建默认商品列表"""
        return [
            {
                "warehouse": 0,
                "goodsDetail": {
                    "customerSkuCode": "自动化标品************",
                    "brandName": "自动化",
                    "skuBrand": "DCPP0219",
                    "firstCategoryName": "卫浴",
                    "firstCategoryLevel": "073",
                    "iotSkuCode": "SP730219000311",
                    "skuName": "自动化标品************",
                    "specifications": "100*100*100",
                    "volume": "0.001000000",
                    "grossWeight": "10.00",
                    "length": 100,
                    "width": 100,
                    "height": 100,
                    "id": "1943369628601622530",
                    "batchNumber": 1,
                    "customizedProduct": 0,
                    "installed": None,
                    "orderNo": None,
                    "packageQuantity": None,
                    "quantity": 1,
                    "skuStatus": None,
                    "skuType": None,
                    "mdmSkuId": None,
                    "belongRelatedNo": None,
                    "serialNumber": None,
                    "secondCategoryLevel": None,
                    "thirdCategoryLevel": None,
                    "brandCode": None,
                    "specialType": None,
                    "relOrderNo": None,
                    "asnNo": "",
                    "lineNum": ""
                }
            },
            {
                "warehouse": 0,
                "goodsDetail": {
                    "customerSkuCode": "自动化定制************",
                    "brandName": "自动化",
                    "skuBrand": "DCPP0219",
                    "firstCategoryName": "卫浴",
                    "firstCategoryLevel": "073",
                    "iotSkuCode": "SP730219000312",
                    "skuName": "自动化定制************",
                    "specifications": "100*100*100",
                    "volume": "0.001000000",
                    "grossWeight": "10.00",
                    "length": 100,
                    "width": 100,
                    "height": 100,
                    "id": "1943369657890447362",
                    "batchNumber": 1,
                    "customizedProduct": 1,
                    "installed": None,
                    "orderNo": None,
                    "packageQuantity": None,
                    "quantity": 1,
                    "skuStatus": None,
                    "skuType": None,
                    "mdmSkuId": None,
                    "belongRelatedNo": None,
                    "serialNumber": None,
                    "secondCategoryLevel": None,
                    "thirdCategoryLevel": None,
                    "brandCode": None,
                    "specialType": None,
                    "relOrderNo": None,
                    "asnNo": "",
                    "lineNum": ""
                }
            }
        ]

    def _build_workflow(self, intent_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """构建工单流程 - 按照标准格式"""
        workflows = []

        # 获取是否提货和状态
        pickup = intent_data.get("pickup", False)
        status = intent_data.get("status", "")

        # 入库单流程节点 (默认：上架)
        if status in ["收货"]:
            workflows.append({"index": "收货", "workOrderTypeName": "采购入库"})
        elif status in ["生成上架单"]:
            workflows.append({"index": "生成上架单", "workOrderTypeName": "采购入库"})
        else:
            # 默认：上架
            workflows.append({"index": "上架", "workOrderTypeName": "采购入库"})

        # 如果是提货，添加配送单流程节点
        if pickup:
            if status in ["合单"]:
                workflows.append({"index": "合单", "workOrderTypeName": "提货"})
            elif status in ["派单"]:
                workflows.append({"index": "派单", "workOrderTypeName": "提货"})
            elif status in ["发车"]:
                workflows.append({"index": "发车", "workOrderTypeName": "提货"})
            else:
                # 默认：签收
                workflows.append({"index": "签收", "workOrderTypeName": "提货"})

        return workflows

    def _create_default_goods(self) -> Dict[str, Any]:
        """创建默认商品信息"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return {
            "customerSkuCode": f"AI_SKU_{timestamp}",
            "brandName": "AI生成品牌",
            "skuBrand": "AI001",
            "firstCategoryName": "AI生成分类",
            "firstCategoryLevel": "AI1",
            "iotSkuCode": f"ai_iot_{timestamp}",
            "skuName": "AI生成商品",
            "specifications": "AI生成规格",
            "volume": "1.0",
            "grossWeight": "1.0",
            "length": 100,
            "width": 100,
            "height": 100,
            "id": f"ai_goods_{timestamp}"
        }

    def _build_default_order_params(self, intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建默认订单参数 - 严格按照最新正确格式"""
        logger.info("🔧 构建默认订单参数")

        # 默认设置：pickup=true（根据最新正确JSON）
        pickup = True

        # 根据订单状态可以调整pickup值
        delivery_statuses = ["合单", "派单", "发车", "签收"]
        status = intent_data.get("status", "")
        if status in delivery_statuses:
            pickup = True

        # 构建工作流程 - 默认包含上架和签收（根据最新正确JSON）
        word_order_req_list = [
            {"index": "上架", "workOrderTypeName": "采购入库"},
            {"index": "签收", "workOrderTypeName": "提货"}
        ]

        # 根据具体状态调整工作流程
        if status in ["收货"]:
            word_order_req_list = [
                {"index": "收货", "workOrderTypeName": "采购入库"},
                {"index": "签收", "workOrderTypeName": "提货"}
            ]
        elif status in ["生成上架单"]:
            word_order_req_list = [
                {"index": "生成上架单", "workOrderTypeName": "采购入库"},
                {"index": "签收", "workOrderTypeName": "提货"}
            ]
        elif status in ["合单"]:
            word_order_req_list = [
                {"index": "上架", "workOrderTypeName": "采购入库"},
                {"index": "合单", "workOrderTypeName": "提货"}
            ]
        elif status in ["派单"]:
            word_order_req_list = [
                {"index": "上架", "workOrderTypeName": "采购入库"},
                {"index": "派单", "workOrderTypeName": "提货"}
            ]
        elif status in ["发车"]:
            word_order_req_list = [
                {"index": "上架", "workOrderTypeName": "采购入库"},
                {"index": "发车", "workOrderTypeName": "提货"}
            ]

        # 返回完整的默认订单参数 - 严格按照你提供的正确JSON格式
        return {
            "associatedOrderNo": "",
            "customer": {
                "customerCode": "DCKH0028",
                "customerName": "自动化商户",
                "shopCode": "1901485162790879233",
                "shopName": "自动化店铺"
            },
            "index": "",
            "orderGoodsList": [
                {
                    "warehouse": 0,
                    "goodsDetail": {
                        "customerSkuCode": "自动化标品************",
                        "brandName": "自动化",
                        "skuBrand": "DCPP0219",
                        "firstCategoryName": "卫浴",
                        "firstCategoryLevel": "073",
                        "iotSkuCode": "SP730219000350",
                        "skuName": "自动化标品************",
                        "specifications": "100*100*100",
                        "volume": "0.001000000",
                        "grossWeight": "10.00",
                        "length": 100,
                        "width": 100,
                        "height": 100,
                        "id": "1950254997911306242",
                        "batchNumber": 1,
                        "customizedProduct": 0,
                        "installed": None,
                        "orderNo": None,
                        "packageQuantity": None,
                        "quantity": 1,
                        "skuStatus": None,
                        "skuType": None,
                        "mdmSkuId": None,
                        "belongRelatedNo": None,
                        "serialNumber": None,
                        "secondCategoryLevel": None,
                        "thirdCategoryLevel": None,
                        "brandCode": None,
                        "specialType": None,
                        "relOrderNo": None,
                        "asnNo": "",
                        "lineNum": ""
                    }
                },
                {
                    "goodsDetail": {
                        "customerSkuCode": "自动化定制************",
                        "brandName": "自动化",
                        "skuBrand": "DCPP0219",
                        "firstCategoryName": "卫浴",
                        "firstCategoryLevel": "073",
                        "iotSkuCode": "SP730219000351",
                        "skuName": "自动化定制************",
                        "specifications": "100*100*100",
                        "volume": "0.001000000",
                        "grossWeight": "10.00",
                        "length": 100,
                        "width": 100,
                        "height": 100,
                        "id": "1950255025274945538",
                        "batchNumber": 1,
                        "customizedProduct": 1,
                        "installed": None,
                        "orderNo": None,
                        "packageQuantity": None,
                        "quantity": 1,
                        "skuStatus": None,
                        "skuType": None,
                        "mdmSkuId": None,
                        "belongRelatedNo": None,
                        "serialNumber": None,
                        "secondCategoryLevel": None,
                        "thirdCategoryLevel": None,
                        "brandCode": None,
                        "specialType": None,
                        "relOrderNo": None,
                        "asnNo": "",
                        "lineNum": ""
                    },
                    "warehouse": 0
                }
            ],
            "pickup": pickup,
            "type": 1,
            "wordOrderReqList": word_order_req_list
        }


class OrderCreatorAgent:
    """订单创建智能体 - 智能批量创建订单"""

    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager

    async def initialize(self):
        """初始化智能体"""
        logger.info("📦 初始化订单创建智能体...")
        logger.info("✅ 订单创建智能体初始化完成")

    async def create_orders(self, order_template: Dict[str, Any], quantity: int, session_id: str) -> Dict[str, Any]:
        """智能批量创建订单 - 使用num参数一次性创建"""
        import time
        start_time = time.time()

        try:
            logger.info(f"📦 开始批量创建订单 - 数量: {quantity}")

            # 添加num参数到订单模板
            order_params = order_template.copy()
            order_params["num"] = quantity

            logger.info(f"🔧 使用num参数批量创建: {quantity}个订单")

            # 一次性创建所有订单
            result = await self._create_batch_orders(order_params, quantity)

            # 计算耗时
            end_time = time.time()
            duration = end_time - start_time

            if result["success"]:
                logger.info(f"✅ 批量创建成功: {quantity}个订单")

                # 保存到记忆
                await self.memory_manager.add_context(
                    session_id,
                    "batch_order_creation",
                    result,
                    {"agent": "OrderCreatorAgent", "timestamp": datetime.now().isoformat(), "quantity": quantity}
                )

                # 构建详细结果，包含工单流程状态
                detailed_results = [f"✅ 批量创建成功: {quantity}个订单"]

                # 添加工单流程状态
                workflow_results = result.get("workflow_results", [])
                if workflow_results:
                    detailed_results.append("📊 工单流程状态:")
                    for workflow in workflow_results:
                        status_icon = "✅" if workflow.get("success") else "❌"
                        step = workflow.get("step", "")
                        status = workflow.get("status", "")
                        details = workflow.get("details", "")

                        # 格式: ✅ 创建订单 - 成功  详情: DC202507300100012
                        if details and details != f"{step}{status}":
                            detailed_results.append(f"  {status_icon} {step} - {status}  详情: {details}")
                        else:
                            detailed_results.append(f"  {status_icon} {step} - {status}")

                return {
                    "success": True,
                    "message": f"成功创建 {quantity} 个订单",
                    "total_requested": quantity,
                    "success_count": quantity,
                    "failed_count": 0,
                    "success_rate": 100.0,
                    "duration": round(duration, 2),
                    "average_speed": round(quantity / duration, 2) if duration > 0 else 0,
                    "created_orders": result.get("created_orders", []),
                    "workflow_results": workflow_results,
                    "detailed_results": detailed_results,
                    "summary": self._generate_summary_with_time(quantity, quantity, 0, duration)
                }
            else:
                logger.error(f"❌ 批量创建失败: {result.get('error', '未知错误')}")
                # 构建失败情况的详细结果
                detailed_results = [f"❌ 批量创建失败: {result.get('error', '未知错误')}"]

                # 添加工单流程状态（如果有的话）
                workflow_results = result.get("workflow_results", [])
                if workflow_results:
                    detailed_results.append("📊 工单流程状态:")
                    for workflow in workflow_results:
                        status_icon = "✅" if workflow.get("success") else "❌"
                        step = workflow.get("step", "")
                        status = workflow.get("status", "")
                        details = workflow.get("details", "")

                        # 格式: ✅ 创建订单 - 成功  详情: DC202507300100012
                        if details and details != f"{step}{status}":
                            detailed_results.append(f"  {status_icon} {step} - {status}  详情: {details}")
                        else:
                            detailed_results.append(f"  {status_icon} {step} - {status}")

                return {
                    "success": False,
                    "message": f"批量创建失败: {result.get('error', '未知错误')}",
                    "total_requested": quantity,
                    "success_count": 0,
                    "failed_count": quantity,
                    "success_rate": 0.0,
                    "duration": round(duration, 2),
                    "average_speed": 0.0,
                    "created_orders": [],
                    "workflow_results": workflow_results,
                    "detailed_results": detailed_results,
                    "summary": self._generate_summary_with_time(quantity, 0, quantity, duration)
                }

        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"❌ 批量创建订单异常: {e}")
            return {
                "success": False,
                "message": f"批量创建异常: {str(e)}",
                "total_requested": quantity,
                "success_count": 0,
                "failed_count": quantity,
                "success_rate": 0.0,
                "duration": round(duration, 2),
                "average_speed": 0.0,
                "created_orders": [],
                "detailed_results": [f"❌ 系统异常: {str(e)}"],
                "summary": self._generate_summary_with_time(quantity, 0, quantity, duration)
            }

    async def _create_single_order(self, order_params: Dict[str, Any], order_index: int) -> Dict[str, Any]:
        """创建单个订单"""
        try:
            # 调用洞车API创建订单
            result = await create_order_rk(num=1, order_data=order_params)

            if result.get("code") == "200":
                # 解析工单流程
                workflow_data = result.get("data", [])
                workflow_steps = []

                for step in workflow_data:
                    step_info = {
                        "index": step.get("index", ""),
                        "workOrderTypeName": step.get("workOrderTypeName", ""),
                        "status": "pending"
                    }
                    workflow_steps.append(step_info)

                # 构建订单信息
                order_info = {
                    "order_index": order_index,
                    "batch_number": order_params.get("orderGoodsList", [{}])[0].get("goodsDetail", {}).get("batchNumber", ""),
                    "customer_code": order_params.get("customer", {}).get("customerCode", ""),
                    "customer_name": order_params.get("customer", {}).get("customerName", ""),
                    "shop_code": order_params.get("customer", {}).get("shopCode", ""),
                    "shop_name": order_params.get("customer", {}).get("shopName", ""),
                    "order_type": "入库单" if order_params.get("pickup") else "出库单",
                    "workflow_steps": workflow_steps,
                    "created_at": datetime.now().isoformat()
                }

                return {
                    "success": True,
                    "message": f"工单流程: {' → '.join([step['workOrderTypeName'] for step in workflow_steps])}",
                    "order_info": order_info,
                    "api_response": result
                }
            else:
                return {
                    "success": False,
                    "error": result.get("message", "API返回错误"),
                    "api_response": result
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"API调用异常: {str(e)}"
            }

    async def _create_batch_orders(self, order_params: Dict[str, Any], quantity: int) -> Dict[str, Any]:
        """批量创建订单 - 使用num参数"""
        try:
            logger.info(f"🔧 调用批量创建API - 参数包含num={quantity}")

            # 调用创建订单API
            from qtools import create_order_rk
            result = await create_order_rk(num=quantity, order_data=order_params)

            if result.get("code") == "200":
                logger.info(f"✅ 批量创建API调用成功")

                # 解析工单流程数据
                workflow_data = result.get("data", [])
                workflow_results = self._parse_workflow_results(workflow_data)

                # 构建成功响应
                created_orders = []
                for i in range(quantity):
                    order_info = {
                        "order_index": i + 1,
                        "customer_code": order_params.get("customer", {}).get("customerCode", ""),
                        "customer_name": order_params.get("customer", {}).get("customerName", ""),
                        "shop_code": order_params.get("customer", {}).get("shopCode", ""),
                        "shop_name": order_params.get("customer", {}).get("shopName", ""),
                        "order_type": "入库单",
                        "workflow_steps": workflow_results,
                        "created_at": datetime.now().isoformat()
                    }
                    created_orders.append(order_info)

                return {
                    "success": True,
                    "message": result.get("message", "批量创建成功"),
                    "created_orders": created_orders,
                    "workflow_results": workflow_results,
                    "api_response": result
                }
            else:
                # 即使失败也尝试解析工单流程数据
                workflow_data = result.get("data", [])
                workflow_results = self._parse_workflow_results(workflow_data) if workflow_data else []

                return {
                    "success": False,
                    "error": result.get("message", "API返回错误"),
                    "workflow_results": workflow_results,
                    "api_response": result
                }

        except Exception as e:
            logger.error(f"❌ 批量创建订单失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _prepare_order_params(self, template: Dict[str, Any], order_index: int) -> Dict[str, Any]:
        """准备订单参数，为每个订单生成唯一标识"""
        order_params = json.loads(json.dumps(template))  # 深拷贝

        # 生成唯一的批次号
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_batch = f"AI{timestamp}{order_index:03d}"

        # 更新批次号
        if "orderGoodsList" in order_params and order_params["orderGoodsList"]:
            goods_detail = order_params["orderGoodsList"][0].get("goodsDetail", {})
            goods_detail["batchNumber"] = unique_batch

            # 更新其他唯一标识
            goods_detail["iotSkuCode"] = f"ai_iot_{timestamp}_{order_index}"
            if goods_detail.get("id"):
                goods_detail["id"] = f"{goods_detail['id']}_{order_index}"

        return order_params

    def _generate_summary(self, total: int, success: int, failed: int) -> str:
        """生成创建结果摘要"""
        success_rate = round((success / total) * 100, 2) if total > 0 else 0

        summary = f"📊 批量订单创建完成\n"
        summary += f"📈 总计: {total} 个订单\n"
        summary += f"✅ 成功: {success} 个\n"
        summary += f"❌ 失败: {failed} 个\n"
        summary += f"📊 成功率: {success_rate}%\n"

        if success_rate >= 90:
            summary += "🎉 创建效果优秀！"
        elif success_rate >= 70:
            summary += "👍 创建效果良好"
        elif success_rate >= 50:
            summary += "⚠️ 创建效果一般，建议检查参数"
        else:
            summary += "🚨 创建效果较差，请检查配置"

        return summary

    def _generate_summary_with_time(self, total: int, success: int, failed: int, duration: float) -> str:
        """生成包含时间统计的创建结果摘要"""
        success_rate = round((success / total) * 100, 2) if total > 0 else 0
        average_speed = round(success / duration, 2) if duration > 0 and success > 0 else 0

        summary = f"📊 批量订单创建完成\n"
        summary += f"📈 总计: {total} 个订单\n"
        summary += f"✅ 成功: {success} 个\n"
        summary += f"❌ 失败: {failed} 个\n"
        summary += f"📊 成功率: {success_rate}%\n"
        summary += f"⏱️ 耗时: {duration:.2f} 秒\n"
        summary += f"🚀 平均速度: {average_speed} 个/秒\n"

        if success_rate >= 90:
            summary += "🎉 创建效果优秀！"
        elif success_rate >= 70:
            summary += "👍 创建效果良好"
        elif success_rate >= 50:
            summary += "⚠️ 创建效果一般，建议检查参数"
        else:
            summary += "❌ 创建效果较差，请检查配置"

        return summary

    def _parse_workflow_results(self, workflow_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析工单流程结果"""
        workflow_results = []

        for step in workflow_data:
            index = step.get("index", "")
            work_order_type = step.get("workOrderTypeName", "")

            # 判断状态是否成功
            is_success = self._determine_step_status(index, work_order_type)
            status_text = "成功" if is_success else "失败"

            # 格式化显示文本
            display_text = f"{index} - {status_text}"

            workflow_result = {
                "step": index,
                "status": status_text,
                "success": is_success,
                "display_text": display_text,
                "work_order_type": work_order_type,
                "details": work_order_type
            }

            workflow_results.append(workflow_result)

        return workflow_results

    def _determine_step_status(self, index: str, work_order_type: str) -> bool:
        """判断工单步骤的成功失败状态"""
        # 根据workOrderTypeName判断状态
        success_indicators = [
            "成功", "完成", "SUCCESS", "success", "OK", "ok"
        ]

        failure_indicators = [
            "失败", "错误", "异常", "FAILED", "failed", "ERROR", "error"
        ]

        work_order_lower = work_order_type.lower()

        # 检查失败指示器
        for indicator in failure_indicators:
            if indicator.lower() in work_order_lower:
                return False

        # 检查成功指示器
        for indicator in success_indicators:
            if indicator.lower() in work_order_lower:
                return True

        # 如果包含订单号格式（DC、ASN、PS开头），认为是成功
        if any(work_order_type.startswith(prefix) for prefix in ["DC", "ASN", "PS"]):
            return True

        # 默认认为是成功（因为API返回200表示操作成功）
        return True


class ResultSummaryAgent:
    """结果汇总智能体 - 智能生成执行报告"""

    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager

    async def initialize(self):
        """初始化智能体"""
        logger.info("📋 初始化结果汇总智能体...")
        logger.info("✅ 结果汇总智能体初始化完成")

    async def generate_summary(self, all_results: Dict[str, Any], session_id: str) -> str:
        """智能生成汇总报告"""
        try:
            logger.info("📋 开始生成汇总报告...")

            # 提取各阶段结果
            intent = all_results.get("intent", {})
            base_data = all_results.get("base_data", {})
            order_template = all_results.get("order_template", {})
            creation_result = all_results.get("creation_result", {})

            # 生成详细报告
            report = self._build_comprehensive_report(
                intent, base_data, order_template, creation_result
            )

            # 保存到记忆
            await self.memory_manager.add_context(
                session_id,
                "summary_report",
                {
                    "report": report,
                    "statistics": self._extract_statistics(creation_result),
                    "recommendations": self._generate_recommendations(creation_result)
                },
                {"agent": "ResultSummaryAgent", "timestamp": datetime.now().isoformat()}
            )

            logger.info("✅ 汇总报告生成完成")
            return report

        except Exception as e:
            logger.error(f"❌ 结果汇总失败: {e}")
            return f"报告生成失败: {str(e)}"

    def _build_comprehensive_report(self, intent: Dict, base_data: Dict,
                                  order_template: Dict, creation_result: Dict) -> str:
        """构建综合报告"""

        # 报告头部
        report = "🎉 AI智能造数工具执行报告\n"
        report += "=" * 50 + "\n\n"

        # 1. 需求解析部分
        report += "📋 需求解析结果:\n"
        report += f"• 原始输入: {intent.get('original_input', '未知')}\n"
        report += f"• 订单类型: {intent.get('order_type', '未知')}\n"
        report += f"• 目标数量: {intent.get('quantity', 0)} 个\n"
        report += f"• 指定商户: {intent.get('customer', '未指定')}\n"
        report += f"• 指定店铺: {intent.get('shop', '未指定')}\n"
        report += f"• 订单状态: {intent.get('status', '未指定')}\n"
        report += f"• 解析置信度: {intent.get('confidence', 0):.1%}\n\n"

        # 2. 数据查询部分
        if base_data:
            customer = base_data.get("customer", {})
            query_summary = base_data.get("query_summary", {})

            report += "🔍 数据查询结果:\n"
            report += f"• 匹配商户: {customer.get('customerName', '未知')} ({customer.get('customerCode', '未知')})\n"
            report += f"• 可用店铺: {query_summary.get('shops_count', 0)} 个\n"
            report += f"• 可用商品: {query_summary.get('goods_count', 0)} 个\n"
            report += f"• 数据置信度: {query_summary.get('confidence', 0):.1%}\n\n"

        # 3. 订单创建部分
        report += "📦 订单创建结果:\n"
        if creation_result.get("success"):
            report += f"• 创建状态: ✅ 成功\n"
            report += f"• 请求数量: {creation_result.get('total_requested', 0)} 个\n"
            report += f"• 成功数量: {creation_result.get('success_count', 0)} 个\n"
            report += f"• 失败数量: {creation_result.get('failed_count', 0)} 个\n"
            report += f"• 成功率: {creation_result.get('success_rate', 0)}%\n"
        else:
            report += f"• 创建状态: ❌ 失败\n"
            report += f"• 失败原因: {creation_result.get('error', '未知错误')}\n"

        report += "\n"

        # 4. 详细结果
        if creation_result.get("detailed_results"):
            report += "📊 详细创建结果:\n"
            for result in creation_result["detailed_results"][:10]:  # 只显示前10个
                report += f"  {result}\n"

            if len(creation_result["detailed_results"]) > 10:
                report += f"  ... 还有 {len(creation_result['detailed_results']) - 10} 个结果\n"
            report += "\n"

        # 5. 工单流程信息 - 已在详细结果中显示，这里不重复显示
        # 工单流程状态已经包含在 detailed_results 中，无需重复显示

        # 6. 执行统计
        report += "📈 执行统计:\n"
        report += f"• 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"• 处理耗时: 约 {creation_result.get('total_requested', 0) * 0.3:.1f} 秒\n"
        report += f"• 平均创建速度: {creation_result.get('success_count', 0) / max(creation_result.get('total_requested', 1) * 0.3, 1):.1f} 个/秒\n\n"

        # 7. 建议和提示
        report += "💡 使用建议:\n"
        recommendations = self._generate_recommendations(creation_result)
        for rec in recommendations:
            report += f"• {rec}\n"

        report += "\n" + "=" * 50 + "\n"
        report += "感谢使用AI智能造数工具！🎉"

        return report

    def _extract_statistics(self, creation_result: Dict) -> Dict[str, Any]:
        """提取统计信息"""
        return {
            "total_requested": creation_result.get("total_requested", 0),
            "success_count": creation_result.get("success_count", 0),
            "failed_count": creation_result.get("failed_count", 0),
            "success_rate": creation_result.get("success_rate", 0),
            "execution_time": datetime.now().isoformat()
        }

    def _generate_recommendations(self, creation_result: Dict) -> List[str]:
        """生成使用建议"""
        recommendations = []

        success_rate = creation_result.get("success_rate", 0)

        if success_rate >= 95:
            recommendations.append("创建效果优秀！可以继续使用相同参数")
            recommendations.append("建议保存当前配置作为模板")
        elif success_rate >= 80:
            recommendations.append("创建效果良好，可以适当增加批量数量")
            recommendations.append("建议定期检查失败订单的原因")
        elif success_rate >= 60:
            recommendations.append("创建效果一般，建议检查商户和商品信息")
            recommendations.append("可以尝试减少单次创建数量")
        else:
            recommendations.append("创建效果较差，建议检查网络连接和API配置")
            recommendations.append("建议联系技术支持获取帮助")

        # 通用建议
        recommendations.append("支持创建不同类型的订单（入库单/出库单）")
        recommendations.append("可以指定不同的商户和店铺组合")
        recommendations.append("支持自定义订单状态和工单流程")

        return recommendations


class AIOrderCreatorTeam:
    """AI智能造数工具团队管理器"""

    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager

        # 智能体实例
        self.intent_parser = IntentParserAgent(model_client, memory_manager)
        self.data_query = DataQueryAgent(model_client, memory_manager)
        self.parameter_builder = ParameterBuilderAgent(model_client, memory_manager)
        self.order_creator = OrderCreatorAgent(model_client, memory_manager)
        self.result_summary = ResultSummaryAgent(model_client, memory_manager)

        # 团队状态
        self.is_initialized = False
        self.processing_sessions = set()

    async def initialize(self):
        """初始化所有智能体"""
        if self.is_initialized:
            return

        logger.info("🚀 初始化AI智能造数工具团队...")

        try:
            # 并行初始化所有智能体
            await asyncio.gather(
                self.intent_parser.initialize(),
                self.data_query.initialize(),
                self.parameter_builder.initialize(),
                self.order_creator.initialize(),
                self.result_summary.initialize()
            )

            self.is_initialized = True
            logger.info("✅ AI智能造数工具团队初始化完成")

        except Exception as e:
            logger.error(f"❌ AI智能造数工具团队初始化失败: {e}")
            raise

    async def process_order_request(self, user_input: str, session_id: str):
        """智能处理订单创建请求"""
        if not self.is_initialized:
            yield {"step": "error", "status": "failed", "message": "AI团队未初始化"}
            return

        if session_id in self.processing_sessions:
            yield {"step": "error", "status": "failed", "message": "该会话正在处理中，请稍后再试"}
            return

        self.processing_sessions.add(session_id)

        try:
            logger.info(f"🎯 开始处理AI造数请求 - 会话: {session_id}")

            # 步骤1: 意图解析
            yield {
                "step": "intent_parsing",
                "status": "start",
                "message": "🧠 正在智能解析您的需求...",
                "progress": 10
            }

            intent_data = await self.intent_parser.parse_intent(user_input, session_id)

            if "error" in intent_data:
                yield {"step": "error", "status": "failed", "message": f"意图解析失败: {intent_data['error']}"}
                return

            yield {
                "step": "intent_parsing",
                "status": "complete",
                "message": f"✅ 需求解析完成 - 将创建 {intent_data.get('quantity', 1)} 个{intent_data.get('order_type', '订单')}",
                "result": intent_data,
                "progress": 20
            }

            # 步骤2: 数据查询
            yield {
                "step": "data_query",
                "status": "start",
                "message": f"🔍 正在查询商户「{intent_data.get('customer', '未指定')}」的基础数据...",
                "progress": 30
            }

            base_data = await self.data_query.query_base_data(intent_data, session_id)

            if "error" in base_data:
                yield {"step": "error", "status": "failed", "message": f"数据查询失败: {base_data['error']}"}
                return

            customer_name = base_data.get("customer", {}).get("customerName", "未知")
            shops_count = len(base_data.get("shops", []))
            goods_count = len(base_data.get("goods", []))

            yield {
                "step": "data_query",
                "status": "complete",
                "message": f"✅ 数据查询完成 - 商户: {customer_name}, 店铺: {shops_count}个, 商品: {goods_count}个",
                "result": base_data,
                "progress": 40
            }

            # 步骤3: 参数构建
            yield {
                "step": "parameter_building",
                "status": "start",
                "message": "🔧 正在智能构建订单参数...",
                "progress": 50
            }

            order_template = await self.parameter_builder.build_parameters(intent_data, base_data, session_id)

            if "error" in order_template:
                yield {"step": "error", "status": "failed", "message": f"参数构建失败: {order_template['error']}"}
                return

            workflow_count = len(order_template.get("wordOrderReqList", []))
            yield {
                "step": "parameter_building",
                "status": "complete",
                "message": f"✅ 参数构建完成 - 工单流程: {workflow_count}个步骤",
                "result": order_template,
                "progress": 60
            }

            # 步骤4: 批量创建订单
            quantity = intent_data.get("quantity", 1)
            yield {
                "step": "order_creation",
                "status": "start",
                "message": f"📦 正在批量创建 {quantity} 个订单，请稍候...",
                "progress": 70
            }

            creation_result = await self.order_creator.create_orders(order_template, quantity, session_id)

            success_count = creation_result.get("success_count", 0)
            success_rate = creation_result.get("success_rate", 0)

            yield {
                "step": "order_creation",
                "status": "complete",
                "message": f"✅ 订单创建完成 - 成功: {success_count}/{quantity}, 成功率: {success_rate}%",
                "result": creation_result,
                "progress": 90
            }

            # 步骤5: 结果汇总
            yield {
                "step": "summary",
                "status": "start",
                "message": "📋 正在生成详细报告...",
                "progress": 95
            }

            all_results = {
                "intent": intent_data,
                "base_data": base_data,
                "order_template": order_template,
                "creation_result": creation_result
            }

            summary_report = await self.result_summary.generate_summary(all_results, session_id)

            yield {
                "step": "summary",
                "status": "complete",
                "message": "🎉 AI智能造数完成！",
                "result": summary_report,
                "progress": 100
            }

            # 保存完整结果到记忆
            await self.memory_manager.add_context(
                session_id,
                "ai_order_complete",
                all_results,
                {
                    "agent": "AIOrderCreatorTeam",
                    "timestamp": datetime.now().isoformat(),
                    "success": creation_result.get("success", False),
                    "total_orders": quantity,
                    "success_orders": success_count
                }
            )

            logger.info(f"🎉 AI造数请求处理完成 - 会话: {session_id}, 成功率: {success_rate}%")

        except Exception as e:
            logger.error(f"❌ AI造数流程失败: {e}")
            yield {"step": "error", "status": "failed", "message": f"处理过程中出现异常: {str(e)}"}

        finally:
            self.processing_sessions.discard(session_id)

    async def get_team_status(self) -> Dict[str, Any]:
        """获取团队状态"""
        return {
            "initialized": self.is_initialized,
            "processing_sessions": len(self.processing_sessions),
            "agents": {
                "intent_parser": "ready",
                "data_query": "ready",
                "parameter_builder": "ready",
                "order_creator": "ready",
                "result_summary": "ready"
            }
        }

    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理AI智能造数工具团队资源...")

        # 等待所有处理中的会话完成
        while self.processing_sessions:
            logger.info(f"等待 {len(self.processing_sessions)} 个会话处理完成...")
            await asyncio.sleep(1)

        self.is_initialized = False
        logger.info("✅ AI智能造数工具团队资源清理完成")
