#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端点检查工具

检查所有注册的端点，确保客户端请求的路径都有对应的处理
"""

import sys
from main import app

def check_endpoints():
    """检查所有注册的端点"""
    print("🔍 检查已注册的端点...")
    print("=" * 60)
    
    routes = []
    for route in app.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            methods = list(route.methods) if route.methods else ['GET']
            routes.append((route.path, methods))
    
    # 按路径排序
    routes.sort(key=lambda x: x[0])
    
    print(f"📊 总共注册了 {len(routes)} 个路由:")
    print()
    
    # 分类显示
    api_routes = []
    frontend_routes = []
    static_routes = []
    websocket_routes = []
    
    for path, methods in routes:
        if path.startswith('/api'):
            api_routes.append((path, methods))
        elif path.startswith('/ws'):
            websocket_routes.append((path, methods))
        elif path in ['/', '/chat', '/order', '/monitor', '/settings', '/app']:
            frontend_routes.append((path, methods))
        else:
            static_routes.append((path, methods))
    
    # API 端点
    if api_routes:
        print("🔌 API 端点:")
        for path, methods in api_routes:
            methods_str = ', '.join(methods)
            print(f"   {methods_str:<20} {path}")
        print()
    
    # WebSocket 端点
    if websocket_routes:
        print("🌐 WebSocket 端点:")
        for path, methods in websocket_routes:
            print(f"   WS                   {path}")
        print()
    
    # 前端路由
    if frontend_routes:
        print("🖥️  前端路由:")
        for path, methods in frontend_routes:
            methods_str = ', '.join(methods)
            print(f"   {methods_str:<20} {path}")
        print()
    
    # 静态资源
    if static_routes:
        print("📁 静态资源:")
        for path, methods in static_routes:
            methods_str = ', '.join(methods)
            print(f"   {methods_str:<20} {path}")
        print()
    
    # 检查常见的客户端请求路径
    print("✅ 常见客户端路径检查:")
    common_paths = [
        ('/api/v1/sse/', 'GET'),
        ('/api/v1/sse/123456', 'GET'),
        ('/api/v1/config', 'GET'),
        ('/api/v1/status', 'GET'),
        ('/api/v1/chat', 'POST'),
        ('/api/v1/order', 'POST'),
        ('/api/chat', 'POST'),
        ('/api/order', 'POST'),
        ('/health', 'GET'),
        ('/favicon.ico', 'GET'),
        ('/robots.txt', 'GET'),
        ('/manifest.json', 'GET'),
        ('/ws/test123', 'WS'),
        ('/', 'GET'),
        ('/chat', 'GET'),
        ('/order', 'GET'),
        ('/monitor', 'GET')
    ]
    
    covered_paths = set()
    for path, methods in routes:
        covered_paths.add(path)
        # 处理参数化路径
        if '{' in path:
            # 简单的参数替换检查
            base_path = path.split('{')[0]
            covered_paths.add(base_path)
    
    missing_paths = []
    for check_path, method in common_paths:
        found = False
        for registered_path, registered_methods in routes:
            # 检查精确匹配
            if check_path == registered_path:
                found = True
                break
            # 检查参数化匹配
            if '{' in registered_path:
                # 简单的参数化路径匹配
                pattern = registered_path.replace('{', '').replace('}', '')
                if check_path.startswith(pattern.split('/')[:-1]):
                    found = True
                    break
        
        if found:
            print(f"   ✅ {method:<6} {check_path}")
        else:
            print(f"   ❌ {method:<6} {check_path}")
            missing_paths.append((check_path, method))
    
    if missing_paths:
        print(f"\n⚠️  发现 {len(missing_paths)} 个缺失的路径:")
        for path, method in missing_paths:
            print(f"   {method:<6} {path}")
    else:
        print("\n🎉 所有常见路径都已覆盖！")
    
    print("\n" + "=" * 60)
    print(f"📈 统计:")
    print(f"   API 端点: {len(api_routes)}")
    print(f"   WebSocket: {len(websocket_routes)}")
    print(f"   前端路由: {len(frontend_routes)}")
    print(f"   静态资源: {len(static_routes)}")
    print(f"   总计: {len(routes)}")

if __name__ == "__main__":
    try:
        check_endpoints()
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        sys.exit(1)
