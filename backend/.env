# AutoGen 智能体工具调用示例 - 环境变量配置
# 复制此文件为 .env 并修改相应配置

# ==================== LLM 配置 ====================

# OpenAI API Key（必需）
# 获取地址：https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-a99405e8ede246e8aaaca5aebd683e75

# API 基础URL（可选，默认为 OpenAI 官方）
# OpenAI 官方：https://api.openai.com/v1
# DeepSeek：https://api.deepseek.com/v1
# 其他兼容服务的 URL
OPENAI_BASE_URL=https://api.deepseek.com/v1

# 模型名称（可选，默认 gpt-3.5-turbo）
# OpenAI 模型：gpt-3.5-turbo, gpt-4, gpt-4-turbo
# DeepSeek 模型：deepseek-chat, deepseek-coder
OPENAI_MODEL=deepseek-chat


# 模型参数（可选）
OPENAI_TEMPERATURE=0.7          # 创造性，0-2，越高越有创意
OPENAI_MAX_TOKENS=6048          # 最大输出长度
OPENAI_TOP_P=1.0               # 核采样参数
OPENAI_FREQUENCY_PENALTY=0.0    # 频率惩罚
OPENAI_PRESENCE_PENALTY=0.0     # 存在惩罚
OPENAI_TIMEOUT=60              # 请求超时时间（秒）

# ==================== 应用配置 ====================

# 应用运行配置
APP_HOST=0.0.0.0
APP_PORT=8001
APP_DEBUG=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/autogen_example.log

# ==================== 示例配置说明 ====================

# 1. OpenAI 官方配置示例：
# OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# OPENAI_BASE_URL=https://api.openai.com/v1
# OPENAI_MODEL=gpt-3.5-turbo

# 2. DeepSeek 配置示例：
# OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_MODEL=deepseek-chat

# 3. 本地模型配置示例（如 Ollama）：
# OPENAI_API_KEY=ollama
# OPENAI_BASE_URL=http://localhost:11434/v1
# OPENAI_MODEL=llama2

# ==================== 使用说明 ====================

# 1. 复制此文件为 .env：
#    cp .env.autogen_example .env

# 2. 修改 OPENAI_API_KEY 为您的实际 API Key

# 3. 根据需要修改其他配置项

# 4. 运行示例：
#    python run_autogen_example.py        # 快速演示
#    python autogen_tool_example.py       # 完整演示

# ==================== 注意事项 ====================

# 1. API Key 安全：
#    - 不要将包含真实 API Key 的 .env 文件提交到版本控制
#    - 建议将 .env 添加到 .gitignore

# 2. 网络连接：
#    - 确保网络能够访问配置的 API 地址
#    - 某些地区可能需要代理访问 OpenAI 官方 API

# 3. 费用控制：
#    - 注意 API 调用费用，建议设置使用限额
#    - 测试时可以使用较便宜的模型如 gpt-3.5-turbo

# 4. 模型选择：
#    - gpt-3.5-turbo：速度快，成本低，适合测试
#    - gpt-4：质量高，成本高，适合生产
#    - deepseek-chat：国产模型，性价比高
