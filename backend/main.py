#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
洞车造数工具 - FastAPI 主服务

提供前后端实时通信、智能体编排、订单创建等功能
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, StreamingResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel
import uvicorn

from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入AI造数工具
try:
    from agents.ai_order_creator import AIOrderCreatorTeam
    from agents.memory_manager import MemoryManager
    AI_ORDER_CREATOR_AVAILABLE = True
    logger.info("✅ AI造数工具模块导入成功")
except ImportError as e:
    logger.warning(f"AI造数工具模块导入失败: {e}")
    AI_ORDER_CREATOR_AVAILABLE = False

    # 创建占位符类
    class AIOrderCreatorTeam:
        def __init__(self, model_client, memory_manager):
            pass

        async def initialize(self):
            pass

        async def process_order_request(self, user_input: str, session_id: str):
            yield {
                "step": "error",
                "status": "failed",
                "message": "AI造数工具不可用，请检查配置和依赖"
            }

        async def cleanup(self):
            pass

    class MemoryManager:
        def __init__(self):
            self.memory = {}

        async def initialize(self):
            logger.info("使用占位符内存管理器")

        async def add_context(self, session_id, context_type, data, metadata=None):
            if session_id not in self.memory:
                self.memory[session_id] = []
            self.memory[session_id].append({
                "context_type": context_type,
                "data": data,
                "metadata": metadata,
                "timestamp": datetime.now().isoformat()
            })

        async def get_context(self, session_id, context_type=None):
            return self.memory.get(session_id, [])

        async def cleanup(self):
            self.memory.clear()

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 减少第三方库的日志级别
logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
logging.getLogger("uvicorn.error").setLevel(logging.INFO)

# 全局变量
memory_manager: Optional[MemoryManager] = None
ai_order_creator: Optional[AIOrderCreatorTeam] = None


# ==================== 生命周期管理 ====================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global memory_manager, ai_order_creator

    # 启动时初始化
    logger.info("🚀 启动AI智能造数工具...")

    try:
        # 初始化内存管理器
        memory_manager = MemoryManager()
        await memory_manager.initialize()
        logger.info("✅ 内存管理器初始化完成")

        # 初始化AI造数工具
        if AI_ORDER_CREATOR_AVAILABLE:
            try:
                # 尝试导入LLM客户端，如果失败则使用None
                try:
                    from llm import model_client
                except ImportError:
                    model_client = None
                    logger.warning("LLM模块不可用，使用简化模式")

                ai_order_creator = AIOrderCreatorTeam(model_client, memory_manager)
                await ai_order_creator.initialize()
                logger.info("✅ AI造数工具初始化完成")
            except Exception as e:
                logger.warning(f"AI造数工具初始化失败: {e}")
                ai_order_creator = None

        if AI_ORDER_CREATOR_AVAILABLE and ai_order_creator:
            logger.info("🎉 AI智能造数工具启动成功！")
        else:
            logger.info("⚠️ AI智能造数工具启动失败，功能不可用")

    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        logger.warning("继续以受限模式启动...")

    yield  # 应用运行期间

    # 关闭时清理资源
    logger.info("🛑 关闭AI智能造数工具...")

    # 清理连接资源
    logger.info("清理连接资源...")

    # 清理资源
    if ai_order_creator:
        await ai_order_creator.cleanup()

    if memory_manager:
        await memory_manager.cleanup()

    logger.info("👋 AI智能造数工具已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="洞车造数工具",
    description="基于 AutoGen 智能体的洞车系统数据生成工具",
    version="1.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# ==================== 异常处理器 ====================

@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """404 错误处理器"""
    path = request.url.path

    # 记录未知路径访问（但不记录常见的探测路径和SSE路径）
    ignore_patterns = ['/favicon.ico', '/robots.txt', '/.well-known', '/api/v1/sse']
    if not any(ignore in path for ignore in ignore_patterns):
        logger.info(f"404 - 未知路径访问: {path}")



    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": f"路径 '{path}' 不存在",
            "available_endpoints": [
                "/",
                "/health",
                "/docs",
                "/api/chat",
                "/api/order",
                "/api/v1/sse/",
                "/ws/{session_id}"
            ]
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证错误处理器"""
    return JSONResponse(
        status_code=422,
        content={
            "error": "Validation Error",
            "message": "请求数据验证失败",
            "details": exc.errors()
        }
    )





# ==================== 数据模型 ====================

class OrderRequest(BaseModel):
    """订单创建请求"""
    template: str
    customer_code: str
    goods_list: List[Dict[str, Any]]
    additional_info: Optional[Dict[str, Any]] = None


class ChatMessage(BaseModel):
    """聊天消息"""
    message: str
    session_id: str
    message_type: str = "user"  # user, system, agent


class AgentResponse(BaseModel):
    """智能体响应"""
    agent_name: str
    content: str
    status: str
    timestamp: datetime
    session_id: str





# ==================== AI造数工具端点 ====================





# ==================== REST API 端点 ====================

@app.get("/")
async def root():
    """根路径"""
    return {"message": "洞车造数工具 API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "ai_order_creator": ai_order_creator is not None,
        "memory_manager": memory_manager is not None,
        "ai_available": AI_ORDER_CREATOR_AVAILABLE
    }


@app.get("/favicon.ico")
async def favicon():
    """网站图标"""
    return JSONResponse(status_code=204, content=None)


@app.get("/robots.txt")
async def robots():
    """爬虫协议"""
    return HTMLResponse(content="User-agent: *\nDisallow: /", media_type="text/plain")


# ==================== 前端资源端点 ====================

@app.get("/manifest.json")
async def manifest():
    """PWA 清单文件"""
    return {
        "name": "洞车造数工具",
        "short_name": "洞车工具",
        "description": "基于 AutoGen 智能体的洞车系统数据生成工具",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#ffffff",
        "theme_color": "#409EFF",
        "icons": []
    }


@app.get("/sw.js")
async def service_worker():
    """Service Worker"""
    return HTMLResponse(
        content="// Service Worker for 洞车造数工具\nconsole.log('Service Worker loaded');",
        media_type="application/javascript"
    )


@app.get("/.well-known/security.txt")
async def security_txt():
    """安全信息"""
    return HTMLResponse(
        content="Contact: <EMAIL>\nExpires: 2025-12-31T23:59:59.000Z",
        media_type="text/plain"
    )


# ==================== API 版本端点 ====================

@app.get("/api")
async def api_info():
    """API 信息"""
    return {
        "name": "洞车造数工具 API",
        "version": "1.0.0",
        "versions": ["v1"],
        "endpoints": {
            "v1": "/api/v1/",
            "legacy": "/api/"
        },
        "documentation": "/docs"
    }


@app.get("/api/v1")
async def api_v1_info():
    """API v1 信息"""
    return {
        "version": "v1",
        "endpoints": {
            "config": "/api/v1/config",
            "status": "/api/v1/status",
            "chat": "/api/v1/chat",
            "order": "/api/v1/order",
            "memory": "/api/v1/memory/{session_id}",
            "sse": "/api/v1/sse/{connection_id}",
            "tools": "/api/v1/tools",
            "agents": "/api/v1/agents",
            "websocket": "/api/v1/ws/info"
        }
    }


@app.get("/api/v1/sse/")
@app.get("/api/v1/sse/{connection_id}")
async def sse_endpoint(connection_id: str = "default"):
    """SSE (Server-Sent Events) 端点 - 支持任意连接ID"""
    async def event_stream():
        """事件流生成器"""
        try:
            # 发送初始连接事件
            yield f"data: {json.dumps({'type': 'connected', 'connection_id': connection_id, 'message': 'SSE连接已建立', 'timestamp': datetime.now().isoformat()})}\n\n"

            # 保持连接并定期发送心跳
            while True:
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                yield f"data: {json.dumps({'type': 'heartbeat', 'connection_id': connection_id, 'timestamp': datetime.now().isoformat()})}\n\n"

        except asyncio.CancelledError:
            # 连接被取消时的清理
            yield f"data: {json.dumps({'type': 'disconnected', 'connection_id': connection_id, 'message': 'SSE连接已断开', 'timestamp': datetime.now().isoformat()})}\n\n"
        except Exception as e:
            # 发生错误时
            yield f"data: {json.dumps({'type': 'error', 'connection_id': connection_id, 'message': str(e), 'timestamp': datetime.now().isoformat()})}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )





# ==================== AI造数工具端点 ====================


@app.get("/api/v1/config")
async def get_config():
    """获取前端配置"""
    return {
        "app_name": "AI智能造数工具",
        "version": "1.0.0",
        "features": {
            "ai_order_creator": AI_ORDER_CREATOR_AVAILABLE,
            "natural_language": True,
            "batch_creation": True
        },
        "endpoints": {
            "ai_order": "/api/v1/ai-order",
            "ai_order_simple": "/api/v1/ai-order-simple"
        }
    }


@app.get("/api/v1/status")
async def get_status():
    """获取系统状态"""
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "ai_order_creator": ai_order_creator is not None,
            "memory_manager": memory_manager is not None,
            "ai_available": AI_ORDER_CREATOR_AVAILABLE
        }
    }


@app.get("/api/v1/memory/{session_id}")
async def get_memory(session_id: str):
    """获取会话记忆"""
    if not memory_manager:
        raise HTTPException(status_code=500, detail="内存管理器未初始化")

    memories = await memory_manager.get_context(session_id)
    return {"session_id": session_id, "memories": memories}


@app.delete("/api/v1/memory/{session_id}")
async def clear_memory(session_id: str):
    """清除会话记忆"""
    if not memory_manager:
        raise HTTPException(status_code=500, detail="内存管理器未初始化")

    if session_id in memory_manager.memory:
        del memory_manager.memory[session_id]

    return {"message": f"会话 {session_id} 的记忆已清除"}


@app.get("/api/v1/agents")
async def get_agents_info():
    """获取AI智能体信息"""
    if not AI_ORDER_CREATOR_AVAILABLE:
        return {
            "available": False,
            "message": "AI造数工具不可用",
            "agents": []
        }

    agents = [
        {
            "name": "意图解析智能体",
            "description": "解析用户自然语言需求，提取关键信息",
            "status": "available" if ai_order_creator else "unavailable",
            "type": "intent_parser"
        },
        {
            "name": "数据查询智能体",
            "description": "查询商户、店铺、商品等基础数据",
            "status": "available" if ai_order_creator else "unavailable",
            "type": "data_query"
        },
        {
            "name": "参数构建智能体",
            "description": "根据数据构建完整的订单参数",
            "status": "available" if ai_order_creator else "unavailable",
            "type": "parameter_builder"
        },
        {
            "name": "订单创建智能体",
            "description": "执行批量订单创建操作",
            "status": "available" if ai_order_creator else "unavailable",
            "type": "order_creator"
        },
        {
            "name": "结果汇总智能体",
            "description": "汇总创建结果，生成报告",
            "status": "available" if ai_order_creator else "unavailable",
            "type": "result_summary"
        }
    ]

    return {
        "available": AI_ORDER_CREATOR_AVAILABLE,
        "agents": agents,
        "count": len(agents),
        "ai_order_creator_available": AI_ORDER_CREATOR_AVAILABLE and ai_order_creator is not None
    }


@app.post("/api/v1/ai-order")
async def create_ai_order(request: Request):
    """AI造数接口 - 自然语言创建订单（流式响应）"""
    if not ai_order_creator:
        raise HTTPException(status_code=503, detail="AI造数工具不可用")

    try:
        body = await request.json()
        user_input = body.get("message", "")
        session_id = body.get("session_id", f"ai_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

        if not user_input:
            raise HTTPException(status_code=400, detail="缺少用户输入")

        logger.info(f"🎯 收到AI造数请求: {user_input} (会话: {session_id})")

        # 使用生成器处理流式响应
        async def generate_response():
            try:
                async for step_result in ai_order_creator.process_order_request(user_input, session_id):
                    # 确保数据格式正确
                    data = json.dumps(step_result, ensure_ascii=False)
                    yield f"data: {data}\n\n"

                    # 添加小延迟确保前端能正确接收
                    await asyncio.sleep(0.1)

            except Exception as e:
                error_data = {
                    "step": "error",
                    "status": "failed",
                    "message": f"处理过程中出现异常: {str(e)}"
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
                "X-Accel-Buffering": "no"  # 禁用nginx缓冲
            }
        )

    except Exception as e:
        logger.error(f"AI造数失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/ai-order-simple")
async def create_ai_order_simple(request: Request):
    """AI造数接口 - 简单版本（非流式）"""
    if not ai_order_creator:
        raise HTTPException(status_code=503, detail="AI造数工具不可用")

    try:
        body = await request.json()
        user_input = body.get("message", "")
        session_id = body.get("session_id", f"ai_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

        if not user_input:
            raise HTTPException(status_code=400, detail="缺少用户输入")

        # 收集所有步骤结果
        results = []
        async for step_result in ai_order_creator.process_order_request(user_input, session_id):
            results.append(step_result)

        return {
            "success": True,
            "session_id": session_id,
            "user_input": user_input,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"AI造数失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }





# ==================== 静态文件服务 ====================

# 检查前端文件是否存在
frontend_dist_path = os.path.join(os.path.dirname(__file__), "..", "frontend", "dist")
if os.path.exists(frontend_dist_path):
    # 挂载静态文件目录（前端文件）
    app.mount("/static", StaticFiles(directory=frontend_dist_path), name="static")

    @app.get("/app")
    async def serve_frontend():
        """服务前端应用"""
        try:
            index_path = os.path.join(frontend_dist_path, "index.html")
            with open(index_path, "r", encoding="utf-8") as f:
                html_content = f.read()
            return HTMLResponse(content=html_content)
        except FileNotFoundError:
            return HTMLResponse(
                content="<h1>前端应用未构建</h1><p>请先构建前端应用</p>",
                status_code=404
            )
else:
    @app.get("/app")
    async def serve_frontend():
        """前端应用不存在时的提示"""
        return HTMLResponse(
            content="""
            <h1>前端应用未构建</h1>
            <p>前端应用目录不存在，请按以下步骤操作：</p>
            <ol>
                <li>进入 frontend 目录</li>
                <li>运行 npm install 安装依赖</li>
                <li>运行 npm run build 构建应用</li>
                <li>或者运行 npm run dev 启动开发服务器</li>
            </ol>
            <p>开发模式下，前端服务运行在 <a href="http://localhost:3000">http://localhost:3000</a></p>
            """,
            status_code=404
        )


# ==================== 前端路由支持 ====================

@app.get("/chat")
@app.get("/order")
@app.get("/monitor")
@app.get("/settings")
async def frontend_routes():
    """前端路由支持"""
    return HTMLResponse(
        content="""
        <h1>前端路由</h1>
        <p>这是一个前端路由页面。在生产环境中，这里应该返回前端应用的 index.html。</p>
        <p>当前可用的路由：</p>
        <ul>
            <li><a href="/chat">智能对话</a></li>
            <li><a href="/order">订单创建</a></li>
            <li><a href="/monitor">状态监控</a></li>
            <li><a href="/settings">设置</a></li>
        </ul>
        <p>开发模式下，前端服务运行在 <a href="http://localhost:3000">http://localhost:3000</a></p>
        """,
        status_code=200
    )


# ==================== 通用API端点 ====================




@app.get("/ping")
async def ping():
    """简单的 ping 端点"""
    return {"message": "pong", "timestamp": datetime.now().isoformat()}


@app.get("/version")
async def version():
    """版本信息"""
    return {
        "name": "洞车造数工具",
        "version": "1.0.0",
        "build": datetime.now().strftime("%Y%m%d"),
        "python_version": "3.10+",
        "framework": "FastAPI"
    }


# ==================== 通配符路由（必须放在最后） ====================

@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])
async def catch_all_routes(request: Request, path: str):
    """捕获所有未匹配的路径"""
    method = request.method

    # 处理 OPTIONS 请求
    if method == "OPTIONS":
        return JSONResponse(
            content={"message": "OK"},
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization",
            }
        )

    # 如果是前端路由，返回前端应用
    frontend_routes = ['chat', 'order', 'monitor', 'settings', 'dashboard', 'profile']
    if method == "GET" and (path in frontend_routes or path.startswith('app/')):
        return HTMLResponse(
            content=f"""
            <h1>前端路由: /{path}</h1>
            <p>这是一个前端路由页面。在生产环境中，这里应该返回前端应用的 index.html。</p>
            <p><a href="/">返回首页</a></p>
            <p>开发模式下，前端服务运行在 <a href="http://localhost:3000">http://localhost:3000</a></p>
            """,
            status_code=200
        )

    # 如果是 API 路径，尝试智能路由
    if path.startswith('api/'):
        # 解析 API 路径
        path_parts = path.split('/')

        # 处理 /api/v1/chat/{session_id} 格式
        if len(path_parts) >= 4 and path_parts[1] == 'v1' and path_parts[2] == 'chat':
            session_id = path_parts[3] if len(path_parts) > 3 else None

            if method == "POST":
                # 尝试处理 POST 请求
                try:
                    body = await request.json()
                    # 创建 ChatMessage 对象
                    message = ChatMessage(
                        message=body.get("message", ""),
                        session_id=session_id or body.get("session_id", ""),
                        message_type=body.get("message_type", "user")
                    )
                    return await chat_v1_api(message, session_id)
                except Exception as e:
                    return JSONResponse(
                        content={
                            "error": "Invalid request body",
                            "message": str(e),
                            "expected_format": {
                                "message": "用户消息内容",
                                "session_id": "会话ID（可选）",
                                "message_type": "user"
                            }
                        },
                        status_code=400
                    )
            else:
                return JSONResponse(
                    content={
                        "message": "Chat endpoint found",
                        "endpoint": f"/api/v1/chat/{session_id}" if session_id else "/api/v1/chat",
                        "method": "POST",
                        "session_id": session_id,
                        "note": "请使用 POST 方法发送聊天消息"
                    },
                    status_code=405  # Method Not Allowed for non-POST
                )

        # 处理 /api/v1/order/{session_id} 格式
        if len(path_parts) >= 4 and path_parts[1] == 'v1' and path_parts[2] == 'order':
            session_id = path_parts[3] if len(path_parts) > 3 else None

            if method == "POST":
                # 尝试处理 POST 请求
                try:
                    body = await request.json()
                    # 创建 OrderRequest 对象
                    order_request = OrderRequest(
                        template=body.get("template", ""),
                        customer_code=body.get("customer_code", ""),
                        goods_list=body.get("goods_list", []),
                        additional_info=body.get("additional_info", {})
                    )
                    return await create_order_v1_api(order_request, session_id)
                except Exception as e:
                    return JSONResponse(
                        content={
                            "error": "Invalid request body",
                            "message": str(e),
                            "expected_format": {
                                "template": "订单模板",
                                "customer_code": "客户编码",
                                "goods_list": ["商品列表"],
                                "additional_info": {"附加信息": "可选"}
                            }
                        },
                        status_code=400
                    )
            else:
                return JSONResponse(
                    content={
                        "message": "Order endpoint found",
                        "endpoint": f"/api/v1/order/{session_id}" if session_id else "/api/v1/order",
                        "method": "POST",
                        "session_id": session_id,
                        "note": "请使用 POST 方法创建订单"
                    },
                    status_code=405  # Method Not Allowed for non-POST
                )

        # 其他 API 路径
        return JSONResponse(
            content={
                "error": "API endpoint not found",
                "path": f"/{path}",
                "available_apis": [
                    "/api/v1/config",
                    "/api/v1/status",
                    "/api/v1/chat[/{session_id}]",
                    "/api/v1/order[/{session_id}]",
                    "/api/v1/sse/{connection_id}",
                    "/api/v1/memory/{session_id}"
                ],
                "documentation": "/docs"
            },
            status_code=404
        )

    # 其他路径返回通用 404
    return JSONResponse(
        content={
            "error": "Page not found",
            "path": f"/{path}",
            "message": "请求的页面不存在",
            "suggestions": [
                "检查 URL 拼写",
                "访问 / 查看首页",
                "访问 /docs 查看 API 文档",
                "访问 /health 检查服务状态"
            ]
        },
        status_code=404
    )


# ==================== 主函数 ====================

if __name__ == "__main__":
    # 获取配置
    host = os.getenv("APP_HOST", "0.0.0.0")
    port = int(os.getenv("APP_PORT", "8000"))
    debug = os.getenv("APP_DEBUG", "false").lower() == "true"
    
    print("🚀 启动洞车造数工具")
    print(f"📡 服务地址: http://{host}:{port}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"🔍 健康检查: http://{host}:{port}/health")
    print(f"🔌 WebSocket: ws://{host}:{port}/ws/{{session_id}}")
    print(f"📺 SSE连接: http://{host}:{port}/api/v1/sse/{{connection_id}}")
    print("\n📋 可用端点:")
    print("   GET  /                    - 根路径")
    print("   GET  /health             - 健康检查")
    print("   GET  /api/v1/config      - 前端配置")
    print("   GET  /api/v1/status      - 系统状态")
    print("   GET  /api/v1/sse/{id}    - SSE连接")
    print("   POST /api/v1/chat        - 聊天接口")
    print("   POST /api/v1/order       - 订单创建")
    print("   WS   /ws/{session_id}    - WebSocket")

    # 启动服务
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="warning" if not debug else "info",
        access_log=debug
    )
