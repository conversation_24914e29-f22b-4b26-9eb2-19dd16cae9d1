# 洞车造数工具 - 环境变量配置
# 复制此文件为 .env 并修改相应配置

# ==================== LLM 配置 ====================

# OpenAI API Key（必需）
# 获取地址：https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# API 基础URL（可选，默认为 DeepSeek）
# OpenAI 官方：https://api.openai.com/v1
# DeepSeek：https://api.deepseek.com/v1
# 其他兼容服务的 URL
OPENAI_BASE_URL=https://api.deepseek.com/v1

# 模型名称（可选，默认 deepseek-chat）
# OpenAI 模型：gpt-3.5-turbo, gpt-4, gpt-4-turbo
# DeepSeek 模型：deepseek-chat, deepseek-coder
OPENAI_MODEL=deepseek-chat

# 模型参数（可选）
OPENAI_TEMPERATURE=0.7          # 创造性，0-2，越高越有创意
OPENAI_MAX_TOKENS=2048          # 最大输出长度
OPENAI_TOP_P=1.0               # 核采样参数
OPENAI_FREQUENCY_PENALTY=0.0    # 频率惩罚
OPENAI_PRESENCE_PENALTY=0.0     # 存在惩罚
OPENAI_TIMEOUT=60              # 请求超时时间（秒）

# ==================== 应用配置 ====================

# 应用运行配置
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/dongche_tool.log

# 数据库配置
DATABASE_URL=sqlite:///./dongche_tool.db
MEMORY_DB_PATH=memory.db

# ==================== 洞车API配置 ====================

# 洞车系统API基础URL
DONGCHE_BASE_URL=http://tools-qa.jrdaimao.com/dataCreate/dongChe
DONGCHE_TIMEOUT=30

# ==================== WebSocket配置 ====================

# WebSocket配置
WS_MAX_CONNECTIONS=100
WS_HEARTBEAT_INTERVAL=30

# ==================== 安全配置 ====================

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true

# ==================== 使用说明 ====================

# 1. 复制此文件为 .env：
#    cp .env.example .env

# 2. 修改 OPENAI_API_KEY 为您的实际 API Key

# 3. 根据需要修改其他配置项

# 4. 启动应用：
#    python main.py

# ==================== 配置示例 ====================

# 1. DeepSeek 配置示例（推荐）：
# OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_MODEL=deepseek-chat

# 2. OpenAI 官方配置示例：
# OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# OPENAI_BASE_URL=https://api.openai.com/v1
# OPENAI_MODEL=gpt-3.5-turbo

# 3. 本地模型配置示例（如 Ollama）：
# OPENAI_API_KEY=ollama
# OPENAI_BASE_URL=http://localhost:11434/v1
# OPENAI_MODEL=llama2

# ==================== 注意事项 ====================

# 1. API Key 安全：
#    - 不要将包含真实 API Key 的 .env 文件提交到版本控制
#    - 建议将 .env 添加到 .gitignore

# 2. 网络连接：
#    - 确保网络能够访问配置的 API 地址
#    - 某些地区可能需要代理访问 OpenAI 官方 API

# 3. 费用控制：
#    - 注意 API 调用费用，建议设置使用限额
#    - DeepSeek 相比 OpenAI 更加经济实惠

# 4. 性能优化：
#    - 根据实际需求调整 OPENAI_MAX_TOKENS
#    - 适当调整 OPENAI_TEMPERATURE 以平衡创造性和准确性
