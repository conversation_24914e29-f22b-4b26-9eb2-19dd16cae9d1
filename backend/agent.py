#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
洞车智能体

提供洞车系统的智能查询和数据生成功能
"""

import asyncio
import logging
from dotenv import load_dotenv

# 导入 AutoGen 相关模块
from autogen_agentchat.agents import AssistantAgent

# 导入本地模块
try:
    from backend.llm import model_client
except ImportError:
    from llm import model_client
from tools import (
    query_daily_goods,
    query_daily_rk_order,
    query_order_goods,
    query_order_goods_list,
    query_goods_sku_list,
    query_driver,
    format_goods_info,
    format_order_info,
    format_driver_info,
    DongCheAPIError
)

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 智能体工具函数 ====================

async def get_daily_goods_info() -> str:
    """获取日常商品信息"""
    try:
        result = await query_daily_goods()
        goods_data = result.get("data", [])

        if goods_data:
            formatted_info = format_goods_info(goods_data)
            return f"📦 日常商品列表（共{len(goods_data)}个）:\n{formatted_info}"
        else:
            return "暂无日常商品信息"

    except DongCheAPIError as e:
        return f"获取商品信息失败: {str(e)}"
    except Exception as e:
        return f"系统错误: {str(e)}"


async def get_daily_orders_info() -> str:
    """获取日常订单信息"""
    try:
        result = await query_daily_rk_order()
        order_data = result.get("data", [])

        if order_data:
            formatted_info = format_order_info(order_data)
            return f"📋 日常入库订单（共{len(order_data)}个）:\n{formatted_info}"
        else:
            return "暂无日常订单信息"

    except DongCheAPIError as e:
        return f"获取订单信息失败: {str(e)}"
    except Exception as e:
        return f"系统错误: {str(e)}"


async def get_order_details(order_no: str) -> str:
    """获取订单详情"""
    try:
        result = await query_order_goods(order_no)
        goods_data = result.get("data", [])

        if goods_data:
            details = []
            for i, goods in enumerate(goods_data[:5], 1):
                detail = f"{i}. 批次号: {goods.get('batchNumber', '未知')}"
                detail += f", 数量: {goods.get('quantity', '未知')}"
                detail += f", 状态: {goods.get('skuStatus', '未知')}"
                details.append(detail)

            return f"📦 订单 {order_no} 商品详情（共{len(goods_data)}个）:\n" + "\n".join(details)
        else:
            return f"订单 {order_no} 暂无商品信息"

    except DongCheAPIError as e:
        return f"获取订单详情失败: {str(e)}"
    except Exception as e:
        return f"系统错误: {str(e)}"


async def get_customer_goods(customer_code: str) -> str:
    """获取客户商品信息"""
    try:
        result = await query_order_goods_list(customer_code)
        goods_data = result.get("data", [])

        if goods_data:
            formatted_info = format_goods_info(goods_data)
            return f"👤 客户 {customer_code} 的商品（共{len(goods_data)}个）:\n{formatted_info}"
        else:
            return f"客户 {customer_code} 暂无商品信息"

    except DongCheAPIError as e:
        return f"获取客户商品信息失败: {str(e)}"
    except Exception as e:
        return f"系统错误: {str(e)}"


async def get_driver_info(search_name: str = "") -> str:
    """获取司机信息"""
    try:
        result = await query_driver(search_name)
        driver_data = result.get("data", [])

        if driver_data:
            formatted_info = format_driver_info(driver_data)
            search_text = f"搜索'{search_name}'" if search_name else "全部"
            return f"🚗 司机信息（{search_text}，共{len(driver_data)}个）:\n{formatted_info}"
        else:
            search_text = f"搜索'{search_name}'" if search_name else "系统中"
            return f"{search_text}暂无司机信息"

    except DongCheAPIError as e:
        return f"获取司机信息失败: {str(e)}"
    except Exception as e:
        return f"系统错误: {str(e)}"


# ==================== 智能体定义 ====================

query_agent = AssistantAgent(
    name="dongchetools",
    model_client=model_client,
    system_message="""你是一个洞车造数工具，可以生成洞车系统中的各种数据。

你可以调用以下工具进行信息查询：
- get_daily_goods_info() - 查询日常商品信息
- get_daily_orders_info() - 查询日常入库订单
- get_order_details(order_no) - 查询订单详情
- get_customer_goods(customer_code) - 查询客户商品信息
- get_driver_info(search_name) - 查询司机信息

要求：
1. 如果从返回结果当中不存在用户要查询的信息，请回复"您所查询的信息不存在"
2. 请根据用户需求调用相应的工具获取信息
3. 用中文回复，格式清晰易读

示例用法：
- "查询商品信息" -> 调用 get_daily_goods_info()
- "查询订单DC202507090100001的详情" -> 调用 get_order_details("DC202507090100001")
- "查询客户DCKH0028的商品" -> 调用 get_customer_goods("DCKH0028")
""",
    tools=[
        get_daily_goods_info,
        get_daily_orders_info,
        get_order_details,
        get_customer_goods,
        get_driver_info
    ]
)


# ==================== 主函数 ====================

async def run_query(user_input: str) -> str:
    """
    运行查询并返回结果

    Args:
        user_input: 用户输入的查询内容

    Returns:
        智能体的回复内容
    """
    try:
        # 使用正确的调用方式
        result = await query_agent.run(user_input)

        # 获取最后一条消息作为回复
        if result.messages:
            return result.messages[-1].content
        else:
            return "抱歉，没有收到回复"

    except Exception as e:
        logger.error(f"查询失败: {e}")
        return f"查询失败: {str(e)}"


async def interactive_mode():
    """交互模式"""
    print("🚀 洞车造数工具")
    print("=" * 50)
    print("可用功能：")
    print("- 查询商品信息")
    print("- 查询订单信息")
    print("- 查询订单详情（需要订单号）")
    print("- 查询客户商品（需要客户编码）")
    print("- 查询司机信息")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 50)

    while True:
        try:
            user_input = input("\n👤 请输入你的问题：").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break

            if not user_input:
                print("⚠️  请输入有效的问题")
                continue

            print("🤖 正在查询...")
            response = await run_query(user_input)
            print(f"🤖 洞车助手: {response}")

        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 程序错误: {e}")
            logger.exception("详细错误信息:")

'''
模板解析智能体
'''
model_analyzer = AssistantAgent(
    name="model_analyzer",
    model_client=model_client,
    system_message="""你是一个模板解析智能体，根据用户输入的信息，解析出需要的信息.
    模板示例：
    请帮我创建10条/个入库单/出库单,
    商户：自动化商户
    店铺：自动化店铺
    订单状态：
    
    规则：
    数字代表订单数量，参数为 num
    入库单/出库单代表订单类型，参数为 order_type, 入库单时,order_type = "1"，出库单时,order_type = "2"
    商户代表客户名称，参数为 customer_name
    店铺代表店铺名称，参数为 shop_name
    订单状态代表订单状态，参数为 order_status
    
    输出：
    {
        num: 10,
        order_type: "",
        customer_name: "自动化商户",
        shop_name: "自动化店铺",
        order_status: "已入库"
    }
    
    
    """
)



async def main():
    """主函数"""
    # await interactive_mode()
    content = input("请输入模板：")
    result = await model_analyzer.run(task=content)
    print(type(result.messages[-1].content))
    print(result.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())
