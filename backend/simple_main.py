#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
洞车造数工具 - 简化版主服务

用于测试基本功能，不依赖复杂的智能体系统
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量
active_connections: Dict[str, WebSocket] = {}
messages_store: Dict[str, List[Dict]] = {}


# ==================== 数据模型 ====================

class ChatMessage(BaseModel):
    """聊天消息"""
    message: str
    session_id: str
    message_type: str = "user"


class OrderRequest(BaseModel):
    """订单创建请求"""
    template: str
    customer_code: str
    goods_list: List[Dict[str, Any]]
    additional_info: Optional[Dict[str, Any]] = None


# ==================== 生命周期管理 ====================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 启动洞车造数工具 (简化版)...")
    
    yield  # 应用运行期间
    
    logger.info("🛑 关闭洞车造数工具...")
    
    # 关闭所有 WebSocket 连接
    for connection in active_connections.values():
        try:
            await connection.close()
        except:
            pass
    
    logger.info("👋 洞车造数工具已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="洞车造数工具 (简化版)",
    description="基于 FastAPI 的洞车系统数据生成工具",
    version="1.0.0-simple",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# ==================== WebSocket 连接管理 ====================

class ConnectionManager:
    """WebSocket 连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        """建立连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logger.info(f"📱 客户端 {session_id} 已连接")
    
    def disconnect(self, session_id: str):
        """断开连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
            logger.info(f"📱 客户端 {session_id} 已断开")
    
    async def send_message(self, session_id: str, message: dict):
        """发送消息给特定客户端"""
        if session_id in self.active_connections:
            try:
                await self.active_connections[session_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                self.disconnect(session_id)


connection_manager = ConnectionManager()


# ==================== 模拟智能体处理 ====================

async def simulate_agent_processing(message: str, session_id: str):
    """模拟智能体处理过程"""
    
    # 模拟处理延迟
    await asyncio.sleep(1)
    
    # 根据消息内容返回不同响应
    if "商品" in message or "goods" in message.lower():
        return {
            "agent_name": "模板解析智能体",
            "content": "📦 已查询到以下商品信息:\n1. 商品A - 规格1 - 库存100\n2. 商品B - 规格2 - 库存50\n3. 商品C - 规格3 - 库存200",
            "status": "success"
        }
    elif "订单" in message or "order" in message.lower():
        return {
            "agent_name": "订单创建智能体",
            "content": "📋 订单创建流程:\n1. ✅ 模板解析完成\n2. ✅ 信息验证通过\n3. ✅ 订单创建成功\n订单号: DC202501290001",
            "status": "success"
        }
    elif "验证" in message or "validate" in message.lower():
        return {
            "agent_name": "信息验证智能体",
            "content": "🔍 验证结果:\n✅ 客户编码 DCKH0028 验证通过\n✅ 商品信息验证通过\n✅ 数据格式验证通过",
            "status": "success"
        }
    else:
        return {
            "agent_name": "协调者",
            "content": f"您好！我是洞车造数工具的智能助手。\n\n我可以帮您:\n• 查询商品信息\n• 验证数据有效性\n• 创建订单\n\n您刚才说: {message}\n\n请告诉我您需要什么帮助？",
            "status": "success"
        }


# ==================== WebSocket 端点 ====================

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket 连接端点"""
    await connection_manager.connect(websocket, session_id)
    
    # 初始化消息存储
    if session_id not in messages_store:
        messages_store[session_id] = []
    
    try:
        # 发送欢迎消息
        welcome_message = {
            "type": "system",
            "content": "🎉 欢迎使用洞车造数工具！我可以帮您创建和验证订单。",
            "timestamp": datetime.now().isoformat(),
            "session_id": session_id
        }
        await connection_manager.send_message(session_id, welcome_message)
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理消息
            await handle_websocket_message(session_id, message_data)
            
    except WebSocketDisconnect:
        connection_manager.disconnect(session_id)
    except Exception as e:
        logger.error(f"WebSocket 错误: {e}")
        connection_manager.disconnect(session_id)


async def handle_websocket_message(session_id: str, message_data: dict):
    """处理 WebSocket 消息"""
    try:
        message_type = message_data.get("type", "chat")
        content = message_data.get("content", "")
        
        # 保存用户消息
        user_message = {
            "type": "user",
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "session_id": session_id
        }
        messages_store[session_id].append(user_message)
        
        if message_type == "chat":
            # 发送处理中状态
            processing_message = {
                "type": "agent_status",
                "content": "🤖 智能体正在处理您的请求...",
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            }
            await connection_manager.send_message(session_id, processing_message)
            
            # 模拟智能体处理
            agent_response = await simulate_agent_processing(content, session_id)
            
            # 发送智能体响应
            response_message = {
                "type": "agent_response",
                "agent_name": agent_response["agent_name"],
                "content": agent_response["content"],
                "status": agent_response["status"],
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            }
            await connection_manager.send_message(session_id, response_message)
            
            # 保存智能体响应
            messages_store[session_id].append(response_message)
            
        elif message_type == "order_create":
            # 处理订单创建
            await handle_order_creation(session_id, message_data)
        else:
            # 未知消息类型
            error_message = {
                "type": "error",
                "content": f"未知的消息类型: {message_type}",
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            }
            await connection_manager.send_message(session_id, error_message)
            
    except Exception as e:
        logger.error(f"处理消息失败: {e}")
        error_message = {
            "type": "error",
            "content": f"处理消息时出错: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "session_id": session_id
        }
        await connection_manager.send_message(session_id, error_message)


async def handle_order_creation(session_id: str, message_data: dict):
    """处理订单创建"""
    # 发送处理中状态
    processing_message = {
        "type": "order_status",
        "content": "📦 正在创建订单...",
        "timestamp": datetime.now().isoformat(),
        "session_id": session_id
    }
    await connection_manager.send_message(session_id, processing_message)
    
    # 模拟订单创建过程
    await asyncio.sleep(2)
    
    # 发送成功响应
    success_message = {
        "type": "order_response",
        "agent_name": "订单创建智能体",
        "content": "✅ 订单创建成功！\n订单号: DC202501290001\n工单流程:\n步骤1: 创建订单\n步骤2: 入库成功",
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "session_id": session_id
    }
    await connection_manager.send_message(session_id, success_message)


# ==================== REST API 端点 ====================

@app.get("/")
async def root():
    """根路径"""
    return {"message": "洞车造数工具 API (简化版)", "version": "1.0.0-simple"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "simple",
        "connections": len(connection_manager.active_connections)
    }


@app.post("/api/chat")
async def chat_api(message: ChatMessage):
    """聊天 API 端点"""
    response = await simulate_agent_processing(message.message, message.session_id)
    return {"response": response}


@app.get("/api/memory/{session_id}")
async def get_memory(session_id: str):
    """获取会话记忆"""
    memories = messages_store.get(session_id, [])
    return {"session_id": session_id, "memories": memories}


@app.get("/app")
async def serve_frontend():
    """前端应用提示"""
    return HTMLResponse(
        content="""
        <h1>洞车造数工具 (简化版)</h1>
        <p>后端服务运行正常！</p>
        <h2>API 端点:</h2>
        <ul>
            <li><a href="/docs">API 文档</a></li>
            <li><a href="/health">健康检查</a></li>
            <li>WebSocket: ws://localhost:8000/ws/{session_id}</li>
        </ul>
        <h2>前端开发:</h2>
        <p>请在 frontend 目录运行 <code>npm run dev</code> 启动前端服务</p>
        <p>前端地址: <a href="http://localhost:3000">http://localhost:3000</a></p>
        """,
        status_code=200
    )


# ==================== 主函数 ====================

if __name__ == "__main__":
    # 获取配置
    host = os.getenv("APP_HOST", "0.0.0.0")
    port = int(os.getenv("APP_PORT", "8000"))
    debug = os.getenv("APP_DEBUG", "false").lower() == "true"
    
    print("🚀 启动洞车造数工具 (简化版)")
    print(f"📡 服务地址: http://{host}:{port}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"🔍 健康检查: http://{host}:{port}/health")
    
    # 启动服务
    uvicorn.run(
        "simple_main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
