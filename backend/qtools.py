#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
洞车（dongChe）接口工具集合

提供洞车系统相关的异步接口调用方法，包括：
- 商品相关接口
- 订单相关接口
- SKU相关接口
- 创建入库订单接口
- 司机相关接口
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union
import aiohttp
import asyncio
from urllib.parse import quote
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

# 基础配置
BASE_URL = "http://tools-qa.jrdaimao.com/dataCreate/dongChe"
DEFAULT_TIMEOUT = 60


class DongCheAPIError(Exception):
    """洞车API调用异常"""
    pass


async def _make_request(
    method: str,
    url: str,
    params: Optional[Dict[str, Any]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    timeout: int = DEFAULT_TIMEOUT
) -> Dict[str, Any]:
    """
    通用的HTTP请求方法

    Args:
        method: HTTP方法 (GET, POST)
        url: 请求URL
        params: URL查询参数
        json_data: JSON请求体
        timeout: 超时时间

    Returns:
        响应数据字典

    Raises:
        DongCheAPIError: API调用失败时抛出
    """
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.request(
                method=method,
                url=url,
                params=params,
                json=json_data,
                headers={"Content-Type": "application/json"}
            ) as response:

                # 记录请求信息
                logger.info(f"请求: {method} {url}")
                if params:
                    logger.info(f"查询参数: {params}")
                if json_data:
                    logger.info(f"请求体: {json_data}")

                # 获取响应数据
                response_text = await response.text()
                logger.info(f"响应状态: {response.status}")
                logger.info(f"响应内容: {response_text}")

                # 解析JSON响应
                try:
                    data = json.loads(response_text)
                except json.JSONDecodeError:
                    raise DongCheAPIError(f"响应不是有效的JSON格式: {response_text}")

                # 检查业务状态码
                if data.get("code") != "200":
                    raise DongCheAPIError(f"API返回错误: {data.get('message', '未知错误')}")

                return data

    except aiohttp.ClientError as e:
        logger.error(f"网络请求失败: {e}")
        raise DongCheAPIError(f"网络请求失败: {str(e)}")
    except asyncio.TimeoutError:
        logger.error(f"请求超时: {url}")
        raise DongCheAPIError(f"请求超时: {url}")


# ==================== 商户和店铺相关接口 ====================

async def query_customer(customer_name: str = "", current: int = 1) -> Dict[str, Any]:
    """
    查询商户信息

    Args:
        customer_name: 商户名称，支持模糊查询
        current: 当前页码，默认1

    Returns:
        Dict: API响应结果
    """
    url = f"{BASE_URL}/query/customer"
    params = {
        "customerName": customer_name,
        "current": current
    }

    try:
        result = await _make_request("POST", url, params=params, json_data={})
        logger.info(f"查询商户成功: {customer_name}")
        return result
    except Exception as e:
        error_msg = f"查询商户失败: {str(e)}"
        logger.error(error_msg)
        raise DongCheAPIError(error_msg)


async def query_shop(customer_code: str, current: int = 1) -> Dict[str, Any]:
    """
    查询店铺信息

    Args:
        customer_code: 商户编码
        current: 当前页码，默认1

    Returns:
        Dict: API响应结果
    """
    url = f"{BASE_URL}/query/shop"
    params = {
        "customerCode": customer_code,
        "current": current
    }

    try:
        result = await _make_request("POST", url, params=params, json_data={})
        logger.info(f"查询店铺成功: {customer_code}")
        return result
    except Exception as e:
        error_msg = f"查询店铺失败: {str(e)}"
        logger.error(error_msg)
        raise DongCheAPIError(error_msg)


async def get_token() -> Dict[str, Any]:
    """
    获取Token

    Returns:
        Dict: API响应结果
    """
    url = f"{BASE_URL}/get/token"

    try:
        result = await _make_request("GET", url)
        logger.info("获取Token成功")
        return result
    except Exception as e:
        error_msg = f"获取Token失败: {str(e)}"
        logger.error(error_msg)
        raise DongCheAPIError(error_msg)


# ==================== 商品相关接口 ====================

async def query_daily_goods() -> Dict[str, Any]:
    """
    查询日常商品列表

    Returns:
        包含商品列表的响应数据

    Example:
        # >>> result = await query_daily_goods()
        # >>> print(f"商品数量: {len(result['data'])}")
    """
    url = f"{BASE_URL}/query/dailyGoods"
    return await _make_request("GET", url)


# ==================== 订单相关接口 ====================

async def query_daily_rk_order() -> Dict[str, Any]:
    """
    查询日常入库订单

    Returns:
        包含订单列表的响应数据

    Example:
        # >>> result = await query_daily_rk_order()
        # >>> print(f"订单数量: {len(result['data'])}")
    """
    url = f"{BASE_URL}/query/dailyRkOrder"
    return await _make_request("GET", url)



async def query_order_goods(order_no: str) -> Dict[str, Any]:
    """
    查询订单商品详情

    Args:
        order_no: 订单号，如 "DC202507090100001"

    Returns:
        包含订单商品列表的响应数据

    Example:
        >>> result = await query_order_goods("DC202507090100001")
        >>> print(f"商品数量: {len(result['data'])}")
    """
    url = f"{BASE_URL}/Order/queryOrderGoods"
    params = {"orderNo": order_no}
    json_data = {}
    return await _make_request("POST", url, params=params, json_data=json_data)


async def query_order_goods_list(
    customer_code: str,
    common_name: str = "",
    current: int = 1
) -> Dict[str, Any]:
    """
    查询订单商品列表（分页）

    Args:
        customer_code: 客户编码，如 "DCKH0028"
        common_name: 通用名称，默认为空
        current: 页码，默认为1

    Returns:
        包含商品列表的响应数据

    Example:
        # >>> result = await query_order_goods_list("DCKH0028", current=1)
        # >>> print(f"商品数量: {len(result['data'])}")
    """
    url = f"{BASE_URL}/query/orderGoodsList"
    params = {
        "customerCode": customer_code,
        "commonName": common_name,
        "current": current
    }
    json_data = {}
    return await _make_request("POST", url, params=params, json_data=json_data)


# ==================== SKU相关接口 ====================

async def query_goods_sku_list(commodity_id: str) -> Dict[str, Any]:
    """
    查询商品SKU列表

    Args:
        commodity_id: 商品ID，如 "1901523175221497857"

    Returns:
        包含SKU列表的响应数据

    Example:
        >>> result = await query_goods_sku_list("1901523175221497857")
        >>> print(f"SKU数量: {len(result['data'])}")
    """
    url = f"{BASE_URL}/query/goodsSkuList"
    params = {"commodityId": commodity_id}
    json_data = {}
    return await _make_request("POST", url, params=params, json_data=json_data)


# ==================== 创建入库订单接口 ====================

async def create_order_rk(num: int = 1, order_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    创建入库订单

    Args:
        num: 数量参数，默认为1
        order_data: 订单数据，如果为None则使用默认数据

    Returns:
        包含工单流程列表的响应数据

    Example:
        >>> result = await create_order_rk(num=1)
        >>> print(f"工单步骤: {[step['workOrderTypeName'] for step in result['data']]}")
    """
    url = f"{BASE_URL}/create/OrderRk"
    params = {"num": num}

    # 如果没有提供订单数据，使用默认数据结构
    if order_data is None:
        order_data = {
            "customerInfo": {
                "customerCode": "DCKH0028",
                "customerName": "默认客户"
            },
            "goodsList": [],
            "workOrderType": "入库订单"
        }

    # 创建订单可能需要更长时间，特别是批量创建
    timeout = 120 if num > 5 else 60
    return await _make_request("POST", url, params=params, json_data=order_data, timeout=timeout)


# ==================== 司机相关接口 ====================

async def query_driver(search_name: str = "") -> Dict[str, Any]:
    """
    查询司机信息

    Args:
        search_name: 搜索名称，如 "自动化司机"，为空时查询全部

    Returns:
        包含司机列表的响应数据

    Example:
        >>> # 查询全部司机
        >>> result = await query_driver()
        >>> print(f"司机数量: {len(result['data'])}")

        >>> # 搜索特定司机
        >>> result = await query_driver("自动化司机")
        >>> print(f"匹配司机: {len(result['data'])}")
    """
    url = f"{BASE_URL}/queryDriver"
    params = {"sname": search_name}
    json_data = {}
    return await _make_request("POST", url, params=params, json_data=json_data)


# ==================== 高级组合方法 ====================

async def get_customer_order_summary(customer_code: str) -> Dict[str, Any]:
    """
    获取客户订单汇总信息

    Args:
        customer_code: 客户编码

    Returns:
        包含订单和商品汇总的数据
    """
    try:
        # 并发查询订单和商品信息
        daily_orders_task = query_daily_rk_order()
        order_goods_task = query_order_goods_list(customer_code)

        daily_orders, order_goods = await asyncio.gather(
            daily_orders_task,
            order_goods_task,
            return_exceptions=True
        )

        result = {
            "customer_code": customer_code,
            "daily_orders": daily_orders if not isinstance(daily_orders, Exception) else None,
            "order_goods": order_goods if not isinstance(order_goods, Exception) else None,
            "summary": {
                "total_orders": len(daily_orders.get("data", [])) if not isinstance(daily_orders, Exception) else 0,
                "total_goods": len(order_goods.get("data", [])) if not isinstance(order_goods, Exception) else 0
            }
        }

        return result

    except Exception as e:
        logger.error(f"获取客户订单汇总失败: {e}")
        raise DongCheAPIError(f"获取客户订单汇总失败: {str(e)}")


async def search_goods_and_sku(commodity_id: str) -> Dict[str, Any]:
    """
    搜索商品及其SKU信息

    Args:
        commodity_id: 商品ID

    Returns:
        包含商品和SKU信息的数据
    """
    try:
        # 并发查询商品和SKU信息
        daily_goods_task = query_daily_goods()
        sku_list_task = query_goods_sku_list(commodity_id)

        daily_goods, sku_list = await asyncio.gather(
            daily_goods_task,
            sku_list_task,
            return_exceptions=True
        )

        result = {
            "commodity_id": commodity_id,
            "daily_goods": daily_goods if not isinstance(daily_goods, Exception) else None,
            "sku_list": sku_list if not isinstance(sku_list, Exception) else None,
            "summary": {
                "total_daily_goods": len(daily_goods.get("data", [])) if not isinstance(daily_goods, Exception) else 0,
                "total_skus": len(sku_list.get("data", [])) if not isinstance(sku_list, Exception) else 0
            }
        }

        return result

    except Exception as e:
        logger.error(f"搜索商品和SKU失败: {e}")
        raise DongCheAPIError(f"搜索商品和SKU失败: {str(e)}")


# ==================== 工具函数 ====================

def format_goods_info(goods_data: List[Dict[str, Any]]) -> str:
    """
    格式化商品信息为可读字符串

    Args:
        goods_data: 商品数据列表

    Returns:
        格式化的商品信息字符串
    """
    if not goods_data:
        return "暂无商品信息"

    result = []
    for i, goods in enumerate(goods_data[:2], 1):  # 只显示前2个
        info = f"{i}. {goods.get('skuName', '未知商品')}"
        if goods.get('brandName'):
            info += f" ({goods['brandName']})"
        if goods.get('specifications'):
            info += f" - {goods['specifications']}"
        result.append(info)

    if len(goods_data) > 2:
        result.append(f"... 还有 {len(goods_data) - 2} 个商品")

    return "\n".join(result)


def format_order_info(order_data: List[Dict[str, Any]]) -> str:
    """
    格式化订单信息为可读字符串

    Args:
        order_data: 订单数据列表

    Returns:
        格式化的订单信息字符串
    """
    if not order_data:
        return "暂无订单信息"

    result = []
    for i, order in enumerate(order_data[:5], 1):  # 只显示前5个
        info = f"{i}. 订单号: {order.get('orderNo', '未知')}"
        if order.get('customerOrderNo'):
            info += f" (客户订单: {order['customerOrderNo']})"
        if order.get('saasOrderType'):
            info += f" - 类型: {order['saasOrderType']}"
        result.append(info)

    if len(order_data) > 5:
        result.append(f"... 还有 {len(order_data) - 5} 个订单")

    return "\n".join(result)


def format_driver_info(driver_data: List[Dict[str, Any]]) -> str:
    """
    格式化司机信息为可读字符串

    Args:
        driver_data: 司机数据列表

    Returns:
        格式化的司机信息字符串
    """
    if not driver_data:
        return "暂无司机信息"

    result = []
    for i, driver in enumerate(driver_data[:10], 1):  # 只显示前10个
        info = f"{i}. {driver.get('name', '未知姓名')}"
        if driver.get('code'):
            info += f" (编码: {driver['code']})"
        if driver.get('phoneNumber'):
            info += f" - 电话: {driver['phoneNumber']}"
        result.append(info)

    if len(driver_data) > 10:
        result.append(f"... 还有 {len(driver_data) - 10} 个司机")

    return "\n".join(result)
