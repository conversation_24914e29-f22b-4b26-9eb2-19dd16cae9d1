<template>
  <div class="order-view">
    <el-row :gutter="20" style="height: 100%;">
      <!-- AI造数区域 -->
      <el-col :span="16">
        <el-card class="ai-order-card">
          <template #header>
            <div class="card-header">
              <div class="title">
                <el-icon><Star /></el-icon>
                <span>AI智能造数</span>
              </div>
              <div class="actions">
                <el-button 
                  size="small" 
                  @click="clearHistory"
                  :icon="Delete"
                >
                  清空历史
                </el-button>
              </div>
            </div>
          </template>
          
          <!-- AI对话区域 -->
          <div class="ai-chat-area">
            <!-- 使用说明 -->
            <div v-if="!chatHistory.length" class="usage-guide">
              <el-alert
                title="AI造数工具使用指南"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <p>🎯 <strong>一句话创建订单</strong></p>
                  <p>示例：<code>请帮我创建1个入库单，商户：自动化，店铺：自动化，订单状态：签到</code></p>
                  <br>
                  <p>📋 <strong>支持的参数</strong></p>
                  <ul>
                    <li>数量：10个、5个等</li>
                    <li>类型：入库单、出库单</li>
                    <li>商户：商户名称或编码</li>
                    <li>店铺：店铺名称或编码</li>
                    <li>状态：签到、下发、上架等</li>
                  </ul>
                </template>
              </el-alert>
            </div>
            
            <!-- 对话历史 -->
            <div class="chat-history" ref="chatHistoryRef">
              <div
                v-for="(message, index) in chatHistory"
                :key="index"
                class="chat-message"
                :class="message.type"
              >
                <div class="message-avatar">
                  <el-avatar 
                    :size="32"
                    :icon="message.type === 'user' ? User : Star"
                    :style="getAvatarStyle(message.type)"
                  />
                </div>
                
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender">{{ message.type === 'user' ? '您' : 'AI助手' }}</span>
                    <span class="time">{{ formatTime(message.timestamp) }}</span>
                  </div>
                  
                  <div class="message-body">
                    <div v-if="message.type === 'ai' && message.steps" class="ai-steps">
                      <!-- 整体进度条 -->
                      <div v-if="message.processing || message.progress" class="overall-progress">
                        <el-progress
                          :percentage="message.progress || 0"
                          :status="message.processing ? '' : 'success'"
                        />
                      </div>

                      <!-- 步骤列表 -->
                      <div class="steps-timeline">
                        <div
                          v-for="(step, stepIndex) in message.steps"
                          :key="stepIndex"
                          class="step-item"
                          :class="[step.status, { 'current': step.status === 'start' }]"
                        >
                          <div class="step-indicator">
                            <el-icon class="step-icon">
                              <Loading v-if="step.status === 'start'" class="rotating" />
                              <Check v-else-if="step.status === 'complete'" />
                              <Close v-else-if="step.status === 'failed'" />
                              <Star v-else />
                            </el-icon>
                          </div>

                          <div class="step-content">
                            <div class="step-title">{{ getStepTitle(step.step) }}</div>
                            <div class="step-message">{{ step.message }}</div>
                            <div v-if="step.timestamp" class="step-time">
                              {{ formatStepTime(step.timestamp) }}
                            </div>
                          </div>

                          <!-- 步骤连接线 -->
                          <div
                            v-if="stepIndex < message.steps.length - 1"
                            class="step-connector"
                            :class="{ 'active': step.status === 'complete' }"
                          ></div>
                        </div>
                      </div>

                      <!-- 最终结果 -->
                      <div v-if="message.finalResult" class="final-result">
                        <el-divider>
                          <el-icon><Star /></el-icon>
                          <span>详细报告</span>
                        </el-divider>
                        <div class="result-content">
                          <pre>{{ message.finalResult }}</pre>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="result-actions">
                          <el-button
                            size="small"
                            type="primary"
                            @click="copyResult(message.finalResult)"
                            :icon="Star"
                          >
                            复制报告
                          </el-button>
                          <el-button
                            size="small"
                            @click="downloadResult(message.finalResult)"
                            :icon="Star"
                          >
                            下载报告
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <div v-else class="message-text">{{ message.content }}</div>
                  </div>
                </div>
              </div>
              
              <!-- 正在处理指示器 -->
              <div v-if="isProcessing" class="processing-indicator">
                <div class="chat-message ai">
                  <div class="message-avatar">
                    <el-avatar :size="32" :icon="Star" />
                  </div>
                  <div class="message-content">
                    <div class="processing-animation">
                      <el-icon class="rotating"><Loading /></el-icon>
                      <span>AI正在处理您的请求...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 输入区域 -->
          <div class="input-area">
            <el-input
              v-model="userInput"
              type="textarea"
              :rows="3"
              placeholder="请输入您的造数需求，例如：请帮我创建1个入库单，商户：自动化，店铺：自动化，订单状态：签到"
              @keydown.ctrl.enter="sendMessage"
              :disabled="isProcessing"
            />
            <div class="input-actions">
              <div class="quick-actions">
                <el-button
                  v-for="template in quickTemplates"
                  :key="template.text"
                  size="small"
                  type="primary"
                  plain
                  @click="useTemplate(template.message)"
                  :disabled="isProcessing || template.disabled"
                >
                  {{ template.text }}
                </el-button>
              </div>
              <el-button 
                type="primary" 
                @click="sendMessage"
                :loading="isProcessing"
                :disabled="!userInput.trim()"
                :icon="Star"
              >
                发送
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 侧边栏 -->
      <el-col :span="8">
        <div class="sidebar">
          <!-- 系统状态 -->
          <el-card class="status-card">
            <template #header>
              <span>系统状态</span>
            </template>
            <div class="status-info">
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="AI造数工具">
                  <el-tag :type="aiStatus.available ? 'success' : 'danger'">
                    {{ aiStatus.available ? '可用' : '不可用' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="智能体数量">
                  <el-text>{{ aiStatus.agentCount || 0 }} 个</el-text>
                </el-descriptions-item>
                <el-descriptions-item label="连接状态">
                  <el-tag type="success">
                    已连接
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>

              <div style="margin-top: 15px;">
                <el-button @click="testBackendConnection" size="small" type="primary">
                  测试后端连接
                </el-button>
                <el-button @click="fetchAIStatus" size="small" type="success">
                  刷新AI状态
                </el-button>
              </div>
            </div>
          </el-card>
          
          <!-- 智能体状态 -->
          <el-card class="agents-card">
            <template #header>
              <span>AI智能体</span>
            </template>
            <div class="agents-list">
              <div 
                v-for="agent in aiAgents" 
                :key="agent.name"
                class="agent-item"
              >
                <div class="agent-info">
                  <el-icon><User /></el-icon>
                  <div class="agent-details">
                    <div class="agent-name">{{ agent.name }}</div>
                    <div class="agent-desc">{{ agent.description }}</div>
                  </div>
                </div>
                <el-tag 
                  :type="agent.status === 'available' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ agent.status === 'available' ? '就绪' : '不可用' }}
                </el-tag>
              </div>
            </div>
          </el-card>
          
          <!-- 使用统计 -->
          <el-card class="stats-card">
            <template #header>
              <span>使用统计</span>
            </template>
            <div class="stats-info">
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="今日创建">{{ todayStats.created }}</el-descriptions-item>
                <el-descriptions-item label="成功率">{{ todayStats.successRate }}%</el-descriptions-item>
                <el-descriptions-item label="总计订单">{{ todayStats.total }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
// import { useAppStore } from '@/stores/app'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Star, Delete, User, Loading, Check, Close
} from '@element-plus/icons-vue'
// import axios from 'axios'

// const appStore = useAppStore()

// 生成会话ID
const sessionId = ref(`web_${Date.now()}`)

// 响应式数据
const userInput = ref('')
const chatHistory = ref([])
const isProcessing = ref(false)
const chatHistoryRef = ref(null)

// AI系统状态
const aiStatus = reactive({
  available: false,
  agentCount: 0
})

// AI智能体列表
const aiAgents = ref([])

// 使用统计
const todayStats = reactive({
  created: 0,
  successRate: 95,
  total: 0
})

// 快捷模板
const quickTemplates = [
  { text: '1个入库单', message: '请帮我创建1个入库单，商户：自动化，店铺：自动化，订单状态：签到', disabled: false },
  { text: '5个出库单', message: '请帮我创建5个出库单，商户：自动化，店铺：自动化，订单状态：下发', disabled: true },
  { text: '批量入库', message: '请帮我创建20个入库单，商户：自动化，店铺：自动化，订单状态：上架', disabled: true }
]

// 计算属性
const currentMessage = computed(() => {
  return chatHistory.value.find(msg => msg.type === 'ai' && msg.processing)
})

// 方法
const sendMessage = async () => {
  if (!userInput.value.trim() || isProcessing.value) return

  const message = userInput.value.trim()
  userInput.value = ''

  console.log('🚀 开始发送AI造数请求:', message)

  // 添加用户消息
  addMessage('user', message)

  // 开始处理
  isProcessing.value = true

  try {
    // 创建AI消息占位符
    const aiMessageIndex = addMessage('ai', '', true)
    console.log('📝 创建AI消息占位符，索引:', aiMessageIndex)
    
    // 调用AI造数接口 - 使用fetch处理SSE
    const response = await fetch('/api/v1/ai-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: message,
        session_id: sessionId.value
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 处理流式响应
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim()
              if (jsonStr) {
                const data = JSON.parse(jsonStr)
                console.log('收到SSE数据:', data)
                updateAIMessage(aiMessageIndex, data)
              }
            } catch (e) {
              console.warn('解析SSE数据失败:', e, '原始数据:', line)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
    
  } catch (error) {
    console.error('AI造数失败:', error)
    ElMessage.error(`AI造数失败：${error.message}`)

    // 更新AI消息显示错误
    if (chatHistory.value[aiMessageIndex]) {
      chatHistory.value[aiMessageIndex].steps.push({
        step: 'error',
        status: 'failed',
        message: `连接失败：${error.message}`,
        timestamp: new Date().toISOString()
      })
      chatHistory.value[aiMessageIndex].processing = false
    }
  } finally {
    isProcessing.value = false
    scrollToBottom()
  }
}

const addMessage = (type, content, processing = false) => {
  const message = {
    type,
    content,
    timestamp: new Date().toISOString(),
    processing,
    steps: type === 'ai' ? [] : undefined,
    finalResult: null
  }
  
  chatHistory.value.push(message)
  nextTick(() => scrollToBottom())
  
  return chatHistory.value.length - 1
}

const updateAIMessage = (messageIndex, stepData) => {
  const message = chatHistory.value[messageIndex]
  if (!message) return

  // 更新步骤
  if (stepData.step && stepData.step !== 'error') {
    const existingStepIndex = message.steps.findIndex(s => s.step === stepData.step)

    const stepInfo = {
      step: stepData.step,
      status: stepData.status,
      message: stepData.message || stepData.result,
      progress: stepData.progress || 0,
      timestamp: new Date().toISOString()
    }

    if (existingStepIndex >= 0) {
      message.steps[existingStepIndex] = stepInfo
    } else {
      message.steps.push(stepInfo)
    }

    // 更新整体进度
    if (stepData.progress) {
      message.progress = stepData.progress
    }
  }

  // 处理最终结果
  if (stepData.step === 'summary' && stepData.status === 'complete') {
    message.finalResult = stepData.result
    message.processing = false
    message.progress = 100

    // 显示成功通知
    ElNotification({
      title: 'AI造数完成',
      message: '订单创建任务已完成，请查看详细报告',
      type: 'success',
      duration: 5000
    })
  }

  // 处理错误
  if (stepData.step === 'error') {
    message.steps.push({
      step: 'error',
      status: 'failed',
      message: stepData.message,
      timestamp: new Date().toISOString()
    })
    message.processing = false
    message.progress = 0

    // 显示错误通知
    ElNotification({
      title: 'AI造数失败',
      message: stepData.message,
      type: 'error',
      duration: 8000
    })
  }

  nextTick(() => scrollToBottom())
}

const useTemplate = (template) => {
  userInput.value = template
}

const clearHistory = () => {
  chatHistory.value = []
}

const scrollToBottom = () => {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
      // 确保输入框可见
      const inputArea = document.querySelector('.input-area')
      if (inputArea) {
        inputArea.scrollIntoView({ behavior: 'smooth', block: 'end' })
      }
    }
  })
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getAvatarStyle = (type) => {
  const colors = {
    user: '#409EFF',
    ai: '#67C23A'
  }
  return { backgroundColor: colors[type] || '#909399' }
}

const formatResult = (result) => {
  if (typeof result === 'string') {
    return result.replace(/\n/g, '<br>')
  }
  return JSON.stringify(result, null, 2)
}

const getStepTitle = (step) => {
  const titles = {
    'intent_parsing': '🧠 意图解析',
    'data_query': '🔍 数据查询',
    'parameter_building': '🔧 参数构建',
    'order_creation': '📦 订单创建',
    'summary': '📋 结果汇总'
  }
  return titles[step] || step
}

const formatStepTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const copyResult = async (result) => {
  try {
    await navigator.clipboard.writeText(result)
    ElMessage.success('报告已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadResult = (result) => {
  const blob = new Blob([result], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `AI造数报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ElMessage.success('报告下载完成')
}

// 获取AI系统状态
const fetchAIStatus = async () => {
  try {
    console.log('正在获取AI状态...')
    const response = await fetch('/api/v1/agents')

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    aiStatus.available = data.ai_order_creator_available || false
    aiStatus.agentCount = data.count || 0

    // 显示所有AI智能体
    aiAgents.value = data.agents || []

    console.log('AI状态获取成功:', data)
    ElMessage.success('AI系统连接正常')
  } catch (error) {
    console.error('获取AI状态失败:', error)
    aiStatus.available = false
    aiAgents.value = []
    ElMessage.error(`AI系统连接失败: ${error.message}`)
  }
}

// 测试后端连接
const testBackendConnection = async () => {
  try {
    console.log('测试后端连接...')
    const response = await fetch('/health')

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('后端连接成功:', data)
    ElMessage.success('后端连接正常')
    return true
  } catch (error) {
    console.error('后端连接失败:', error)
    ElMessage.error(`后端连接失败: ${error.message}`)
    return false
  }
}

// 生命周期
onMounted(async () => {
  console.log('OrderView 组件已挂载')

  // 先测试后端连接
  const backendOk = await testBackendConnection()

  if (backendOk) {
    // 后端连接正常，获取AI状态
    await fetchAIStatus()
  } else {
    console.log('后端连接失败，跳过AI状态获取')
  }
})
</script>

<style lang="scss" scoped>
.order-view {
  height: 100%;
  padding: 20px;
}

.ai-order-card {
  height: calc(100vh - 160px);
  display: flex;
  flex-direction: column;
  
  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #409EFF;
  }
}

.ai-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .usage-guide {
    margin: 20px;
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
    }
    
    code {
      background: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
    }
  }
  
  .chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    max-height: calc(100vh - 300px);
    min-height: 400px;
    
    .chat-message {
      display: flex;
      margin-bottom: 20px;
      
      &.user {
        flex-direction: row-reverse;
        
        .message-content {
          margin-right: 12px;
          margin-left: 0;
          
          .message-body {
            background: #409EFF;
            color: white;
          }
        }
      }
      
      .message-avatar {
        flex-shrink: 0;
      }
      
      .message-content {
        flex: 1;
        margin-left: 12px;
        max-width: 80%;
        
        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          
          .sender {
            font-weight: bold;
            font-size: 12px;
          }
          
          .time {
            font-size: 12px;
            color: #999;
          }
        }
        
        .message-body {
          background: #f5f5f5;
          padding: 12px;
          border-radius: 8px;
          
          .message-text {
            line-height: 1.5;
          }
          
          .ai-steps {
            .overall-progress {
              margin-bottom: 16px;
            }

            .steps-timeline {
              position: relative;

              .step-item {
                position: relative;
                display: flex;
                align-items: flex-start;
                gap: 12px;
                margin-bottom: 16px;
                padding: 8px 0;

                &.current {
                  background: rgba(64, 158, 255, 0.05);
                  border-radius: 8px;
                  padding: 12px;
                  margin: 0 -12px 16px -12px;
                }

                .step-indicator {
                  flex-shrink: 0;
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: #f5f5f5;
                  border: 2px solid #e0e0e0;

                  .step-icon {
                    font-size: 16px;

                    &.rotating {
                      animation: rotate 1s linear infinite;
                    }
                  }
                }

                &.start .step-indicator {
                  background: #e3f2fd;
                  border-color: #409EFF;
                  color: #409EFF;
                }

                &.complete .step-indicator {
                  background: #e8f5e8;
                  border-color: #67C23A;
                  color: #67C23A;
                }

                &.failed .step-indicator {
                  background: #ffeaea;
                  border-color: #F56C6C;
                  color: #F56C6C;
                }

                .step-content {
                  flex: 1;

                  .step-title {
                    font-weight: bold;
                    font-size: 14px;
                    margin-bottom: 4px;
                    color: #333;
                  }

                  .step-message {
                    font-size: 13px;
                    color: #666;
                    line-height: 1.4;
                  }

                  .step-time {
                    font-size: 11px;
                    color: #999;
                    margin-top: 4px;
                  }
                }

                .step-connector {
                  position: absolute;
                  left: 15px;
                  top: 40px;
                  width: 2px;
                  height: 24px;
                  background: #e0e0e0;

                  &.active {
                    background: #67C23A;
                  }
                }
              }
            }

            .final-result {
              margin-top: 20px;

              .result-content {
                background: #f9f9f9;
                padding: 16px;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
                max-height: 400px;
                overflow-y: auto;

                pre {
                  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                  font-size: 12px;
                  line-height: 1.5;
                  margin: 0;
                  white-space: pre-wrap;
                  word-wrap: break-word;
                }
              }

              .result-actions {
                margin-top: 12px;
                display: flex;
                gap: 8px;
                justify-content: flex-end;
              }
            }
          }
        }
      }
    }
    
    .processing-indicator {
      .processing-animation {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        
        .rotating {
          animation: rotate 1s linear infinite;
        }
      }
    }
  }
}

.input-area {
  border-top: 1px solid #eee;
  padding: 20px;
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  background: white;
  z-index: 10;
  
  .input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    
    .quick-actions {
      display: flex;
      gap: 8px;

      .el-button.is-disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .status-card,
  .agents-card,
  .stats-card {
    :deep(.el-card__header) {
      padding: 12px 16px;
      font-weight: bold;
    }
    
    :deep(.el-card__body) {
      padding: 16px;
    }
  }
  
  .agents-list {
    .agent-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      
      .agent-info {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        flex: 1;
        
        .agent-details {
          .agent-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
          }
          
          .agent-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
          }
        }
      }
    }
  }
  
  .stats-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
