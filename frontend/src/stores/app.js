import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const sessionId = ref('')
  const currentAgent = ref('')
  const agentStatus = ref({})

  // 设置加载状态
  const setLoading = (status) => {
    loading.value = status
  }

  // 设置会话ID
  const setSessionId = (id) => {
    sessionId.value = id
  }

  // 设置当前智能体
  const setCurrentAgent = (agent) => {
    currentAgent.value = agent
  }

  // 更新智能体状态
  const updateAgentStatus = (agentName, status) => {
    agentStatus.value[agentName] = {
      ...agentStatus.value[agentName],
      ...status,
      lastUpdate: new Date().toISOString()
    }
  }

  // 获取智能体状态
  const getAgentStatus = (agentName) => {
    return agentStatus.value[agentName] || {
      status: 'idle',
      lastUpdate: null
    }
  }

  // 重置状态
  const reset = () => {
    loading.value = false
    currentAgent.value = ''
    agentStatus.value = {}
  }

  return {
    // 状态
    loading,
    sessionId,
    currentAgent,
    agentStatus,
    
    // 方法
    setLoading,
    setSessionId,
    setCurrentAgent,
    updateAgentStatus,
    getAgentStatus,
    reset
  }
})
