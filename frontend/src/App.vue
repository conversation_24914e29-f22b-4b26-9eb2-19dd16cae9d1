<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 顶部导航 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><Star /></el-icon>
            <span>AI智能造数工具</span>
          </div>
          <div class="nav-info">
            <el-tag type="success" size="small">
              <el-icon><User /></el-icon>
              <span>智能造数系统</span>
            </el-tag>
          </div>
          <div class="header-actions">
            <!-- 开始造数按钮已移除 -->
          </div>
        </div>
      </el-header>

      <!-- 主要内容区域 -->
      <el-main class="app-main">
        <router-view />
      </el-main>

      <!-- 底部状态栏 -->
      <el-footer class="app-footer">
        <div class="footer-content">
          <div class="status-info">
            <el-tag type="success" size="small">
              系统运行正常
            </el-tag>
            <span class="session-id">AI智能造数工具</span>
          </div>
          <div class="copyright">
            © 2025 洞车造数工具 v1.0.0
          </div>
        </div>
      </el-footer>
    </el-container>


  </div>
</template>

<script setup>
import { Star, User } from '@element-plus/icons-vue'

console.log('AI智能造数工具启动')
</script>

<style lang="scss" scoped>
.app-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    
    .logo {
      display: flex;
      align-items: center;
      color: white;
      font-size: 20px;
      font-weight: bold;
      
      .el-icon {
        margin-right: 8px;
        font-size: 24px;
      }
    }
    
    .nav-info {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

.app-main {
  background: rgba(255, 255, 255, 0.95);
  margin: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: auto;
}

.app-footer {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  height: 40px !important;
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    color: white;
    font-size: 12px;
    
    .status-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .session-id {
        opacity: 0.8;
      }
    }
    
    .copyright {
      opacity: 0.8;
    }
  }
}

// 全局样式
:deep(.el-loading-mask) {
  backdrop-filter: blur(5px);
}
</style>
