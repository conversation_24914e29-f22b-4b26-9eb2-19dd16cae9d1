import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Order',
    component: () => import('@/views/OrderView.vue'),
    meta: {
      title: 'AI智能造数',
      icon: 'Star'
    }
  },
  {
    path: '/order',
    name: 'OrderPage',
    component: () => import('@/views/OrderView.vue'),
    meta: {
      title: 'AI智能造数',
      icon: 'Star'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
