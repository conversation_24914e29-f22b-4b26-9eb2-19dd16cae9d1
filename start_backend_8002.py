#!/usr/bin/env python3
"""
启动后端服务 - 使用8002端口
"""

import os
import sys
import subprocess

def main():
    # 设置环境变量
    os.environ["APP_PORT"] = "8002"
    os.environ["APP_HOST"] = "0.0.0.0"
    os.environ["APP_DEBUG"] = "false"
    
    print("🚀 启动洞车造数工具后端服务")
    print("📡 服务地址: http://localhost:8002")
    print("📚 API文档: http://localhost:8002/docs")
    print("🔄 前端代理: 3001 -> 8002")
    print("-" * 50)
    
    try:
        # 切换到backend目录
        backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
        os.chdir(backend_dir)
        
        # 启动后端服务
        subprocess.run([sys.executable, "main.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
