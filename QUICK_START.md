# 🚀 AI智能造数工具 - 快速开始

## 📋 系统要求

- Python 3.8+
- 基本的Python包（自动安装）

## ⚡ 3步启动

### 1️⃣ 验证系统
```bash
python verify_system.py
```

### 2️⃣ 启动服务
```bash
# 方式1: 仅启动后端（推荐）
python start_backend_only.py

# 方式2: 启动完整系统（包含前端）
python quick_start.py
```

### 3️⃣ 测试功能
```bash
python test_simple.py
```

## 🎯 使用示例

### API调用
```bash
curl -X POST "http://localhost:8000/api/v1/ai-order-simple" \
  -H "Content-Type: application/json" \
  -d '{"message": "请帮我创建5个入库单，商户：自动化", "session_id": "test"}'
```

### 自然语言示例
- `请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到`
- `创建5个出库单，商户：测试商户`
- `帮我生成3个入库订单，商户：自动化`

## 📊 智能体流程

```
用户输入 → 意图解析 → 数据查询 → 参数构建 → 订单创建 → 结果汇总
```

## 🔧 故障排除

### 问题1: 包缺失
```bash
pip install fastapi uvicorn aiohttp pydantic python-dotenv
```

### 问题2: 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8000  # Windows
lsof -i :8000                 # Linux/Mac

# 或更改端口
cd backend
python -m uvicorn main:app --port 8001
```

### 问题3: 权限问题
```bash
# Windows
python -m pip install --user package_name

# Linux/Mac
pip install --user package_name
```

## 📝 API端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/api/v1/agents` | GET | 智能体状态 |
| `/api/v1/ai-order` | POST | 流式AI造数 |
| `/api/v1/ai-order-simple` | POST | 简单AI造数 |
| `/docs` | GET | API文档 |

## 🎉 成功标志

看到以下信息说明启动成功：
```
✅ 后端服务启动成功 - http://localhost:8000
✅ AI造数工具初始化完成
🎉 AI智能造数系统启动完成！
```

## 💡 使用提示

- 系统运行在**简化模式**，无需配置API Key
- 支持**自然语言**输入，一句话创建订单
- 提供**实时进度**显示和详细报告
- 所有功能都可以正常测试和演示

---

**开始您的AI智能造数之旅！** 🤖✨
