#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能造数系统启动脚本

快速启动和测试AI造数系统
"""

import asyncio
import subprocess
import sys
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖"""
    logger.info("🔍 检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error("❌ Python版本需要3.8或更高")
        return False
    
    # 检查必要的包
    required_packages = [
        'fastapi', 'uvicorn', 'aiohttp', 'pydantic'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r backend/requirements.txt")
        return False
    
    logger.info("✅ 依赖检查通过")
    return True

def start_backend():
    """启动后端服务"""
    logger.info("🚀 启动后端服务...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        logger.error("❌ backend目录不存在")
        return None
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], cwd=backend_dir)
        
        logger.info("✅ 后端服务启动成功 - http://localhost:8000")
        return process
        
    except Exception as e:
        logger.error(f"❌ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    logger.info("🎨 启动前端服务...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        logger.error("❌ frontend目录不存在")
        return None
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        logger.info("📦 安装前端依赖...")
        try:
            subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
        except subprocess.CalledProcessError:
            logger.error("❌ 前端依赖安装失败")
            return None
    
    try:
        # 启动Vite开发服务器
        process = subprocess.Popen([
            "npm", "run", "dev"
        ], cwd=frontend_dir)
        
        logger.info("✅ 前端服务启动成功 - http://localhost:3000")
        return process
        
    except Exception as e:
        logger.error(f"❌ 前端服务启动失败: {e}")
        return None

async def test_ai_system():
    """测试AI系统"""
    logger.info("🧪 测试AI智能造数系统...")
    
    # 等待服务启动
    await asyncio.sleep(3)
    
    try:
        import aiohttp
        
        # 测试健康检查
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ 后端健康检查通过: {data.get('status')}")
                else:
                    logger.warning(f"⚠️ 后端健康检查失败: {response.status}")
            
            # 测试AI智能体状态
            async with session.get("http://localhost:8000/api/v1/agents") as response:
                if response.status == 200:
                    data = await response.json()
                    ai_available = data.get('ai_order_creator_available', False)
                    agent_count = data.get('count', 0)
                    
                    if ai_available:
                        logger.info(f"✅ AI造数工具可用 - {agent_count}个智能体就绪")
                    else:
                        logger.warning("⚠️ AI造数工具不可用，请检查配置")
                else:
                    logger.warning(f"⚠️ 智能体状态查询失败: {response.status}")
            
            # 测试简单的AI造数请求
            test_payload = {
                "message": "请帮我创建1个入库单，商户：测试商户",
                "session_id": f"test_{int(time.time())}"
            }
            
            async with session.post(
                "http://localhost:8000/api/v1/ai-order-simple",
                json=test_payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logger.info("✅ AI造数功能测试通过")
                    else:
                        logger.warning("⚠️ AI造数功能测试失败")
                else:
                    logger.warning(f"⚠️ AI造数接口测试失败: {response.status}")
        
    except Exception as e:
        logger.warning(f"⚠️ 系统测试失败: {e}")

def print_usage_info():
    """打印使用说明"""
    print("\n" + "="*60)
    print("🎉 AI智能造数系统启动完成！")
    print("="*60)
    print()
    print("📋 访问地址:")
    print("  • 前端界面: http://localhost:3000")
    print("  • 后端API: http://localhost:8000")
    print("  • API文档: http://localhost:8000/docs")
    print()
    print("🎯 使用方法:")
    print("  1. 打开前端界面: http://localhost:3000")
    print("  2. 输入自然语言需求，例如:")
    print("     '请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到'")
    print("  3. 查看AI智能体实时处理过程")
    print("  4. 获取详细的执行报告")
    print()
    print("🔧 API接口:")
    print("  • POST /api/v1/ai-order - 流式AI造数接口")
    print("  • POST /api/v1/ai-order-simple - 简单AI造数接口")
    print("  • GET /api/v1/agents - 查看智能体状态")
    print()
    print("⚠️ 注意事项:")
    print("  • 确保已配置环境变量 (OPENAI_API_KEY 等)")
    print("  • 确保洞车API地址可访问")
    print("  • 按 Ctrl+C 停止服务")
    print()
    print("="*60)

async def main():
    """主函数"""
    logger.info("🚀 启动AI智能造数系统...")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        return
    
    # 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        return
    
    try:
        # 测试系统
        await test_ai_system()
        
        # 打印使用说明
        print_usage_info()
        
        # 等待用户中断
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("\n👋 正在关闭服务...")
        
        # 关闭进程
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        
        # 等待进程结束
        if frontend_process:
            frontend_process.wait()
        if backend_process:
            backend_process.wait()
        
        logger.info("✅ 服务已关闭")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        sys.exit(1)
