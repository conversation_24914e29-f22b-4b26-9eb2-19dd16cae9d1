# 🤖 AI智能造数工具

基于AutoGen智能体框架的自然语言订单创建系统

## 🎯 功能特点

- **🧠 自然语言理解**: 一句话创建订单，无需复杂操作
- **⚡ 智能体协作**: 5个专业智能体协同工作
- **📊 实时进度**: 完整的处理过程可视化
- **🎯 批量处理**: 支持大量订单的批量创建
- **📋 详细报告**: 生成完整的执行报告和统计

## 🏗️ 系统架构

```
用户输入: "请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到"
    ↓
[意图解析智能体] → 解析用户需求，提取关键信息
    ↓
[数据查询智能体] → 查询商户、店铺、商品等基础数据
    ↓
[参数构建智能体] → 根据模板和数据构建订单参数
    ↓
[订单创建智能体] → 批量创建订单并返回结果
    ↓
[结果汇总智能体] → 汇总创建结果，生成报告
```

## 🚀 快速开始

### 1. 环境准备

```bash
# Python 3.8+
pip install -r backend/requirements.txt

# Node.js 16+
cd frontend
npm install
```

### 2. 环境变量配置

```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑配置文件
# OPENAI_API_KEY=your_api_key
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_MODEL=deepseek-chat
```

### 3. 一键启动

```bash
# 使用启动脚本
python start_ai_order_system.py

# 或手动启动
# 后端
cd backend && python -m uvicorn main:app --reload

# 前端
cd frontend && npm run dev
```

### 4. 访问系统

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 💬 使用示例

### 自然语言输入

```
请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到
创建5个出库单，商户：北京汇跃，店铺：梵客南店
帮我生成20个入库订单，商户编码：jr001744，状态：下发
```

### API调用

```bash
# 流式接口
curl -X POST "http://localhost:8000/api/v1/ai-order" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请帮我创建5个入库单，商户：自动化",
    "session_id": "test_session"
  }'

# 简单接口
curl -X POST "http://localhost:8000/api/v1/ai-order-simple" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "创建3个出库单，商户：测试商户",
    "session_id": "test_session"
  }'
```

## 🧪 测试系统

```bash
# 运行完整测试
cd backend
python test_ai_order_creator.py

# 选择测试模式
# 1. 完整流程测试
# 2. 单个智能体测试  
# 3. API端点测试
# 4. 全部测试
```

## 📊 实时进度显示

系统会显示完整的处理过程：

```
🧠 意图解析 (20%) → ✅ 需求解析完成 - 将创建 10 个入库单
🔍 数据查询 (40%) → ✅ 数据查询完成 - 商户: 自动化, 店铺: 2个, 商品: 15个  
🔧 参数构建 (60%) → ✅ 参数构建完成 - 工单流程: 3个步骤
📦 订单创建 (90%) → ✅ 订单创建完成 - 成功: 10/10, 成功率: 100%
📋 结果汇总 (100%) → 🎉 AI智能造数完成！
```

## 🔧 配置说明

### 环境变量

```env
# LLM配置
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat

# 洞车API配置
DONGCHE_BASE_URL=http://tools-qa.jrdaimao.com/dataCreate/dongChe
DONGCHE_TIMEOUT=30
```

### 支持的参数

| 参数 | 说明 | 示例 |
|------|------|------|
| 数量 | 创建订单的数量 | 10个、5个、20个 |
| 类型 | 订单类型 | 入库单、出库单 |
| 商户 | 商户名称或编码 | 自动化、jr001744 |
| 店铺 | 店铺名称或编码 | 自动化、梵客南店 |
| 状态 | 订单状态 | 签到、下发、上架 |

## 🛠️ 开发指南

### 项目结构

```
├── backend/                 # 后端服务
│   ├── agents/             # 智能体模块
│   │   ├── ai_order_creator.py  # AI造数智能体
│   │   └── memory_manager.py    # 记忆管理
│   ├── main.py             # 主应用
│   ├── qtools.py           # 洞车API工具
│   └── llm.py              # LLM配置
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── views/OrderView.vue  # AI造数界面
│   │   └── router/index.js      # 路由配置
│   └── package.json
├── start_ai_order_system.py    # 启动脚本
└── test_ai_order_creator.py    # 测试脚本
```

### 添加新智能体

```python
class CustomAgent:
    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager
        
    async def initialize(self):
        # 初始化逻辑
        pass
        
    async def process(self, data, session_id):
        # 处理逻辑
        pass
```

## 🔍 故障排除

### 常见问题

1. **智能体初始化失败**
   - 检查API Key配置
   - 确认网络连接
   - 查看日志错误信息

2. **数据查询失败**
   - 验证洞车API地址
   - 检查商户名称是否正确
   - 确认API权限

3. **订单创建失败**
   - 检查订单参数格式
   - 验证商品信息完整性
   - 查看API响应错误

### 调试方法

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 检查智能体状态
curl http://localhost:8000/api/v1/agents

# 测试单个接口
curl -X POST http://localhost:8000/api/v1/ai-order-simple \
  -H "Content-Type: application/json" \
  -d '{"message": "测试", "session_id": "debug"}'
```

## 📈 性能优化

- **并发控制**: 限制同时处理的请求数量
- **缓存机制**: 缓存常用的查询结果
- **批量处理**: 优化大量订单的创建速度
- **错误重试**: 自动重试失败的操作

## 🎉 特色功能

- **智能化**: 自然语言理解和处理
- **自动化**: 端到端的订单创建流程
- **可扩展**: 模块化的智能体架构
- **可靠性**: 完善的错误处理和重试机制
- **用户友好**: 直观的前端界面和实时反馈

## 📞 技术支持

如有问题，请查看：
- 系统日志: `backend/logs/`
- API文档: http://localhost:8000/docs
- 测试脚本: `python test_ai_order_creator.py`

---

**AI智能造数工具** - 让订单创建变得简单智能！ 🚀
