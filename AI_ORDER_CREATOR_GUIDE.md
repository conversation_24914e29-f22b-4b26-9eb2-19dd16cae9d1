# AI造数工具使用指南

## 🎯 概述

AI造数工具是基于AutoGen智能体框架开发的自然语言订单创建系统。用户只需要一句话描述需求，AI就能自动完成从数据查询到订单创建的全流程。

## 🏗️ 系统架构

### 智能体编排

```
用户输入: "请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到"
    ↓
[意图解析智能体] → 解析用户需求，提取关键信息
    ↓
[数据查询智能体] → 查询商户、店铺、商品等基础数据
    ↓
[参数构建智能体] → 根据模板和数据构建订单参数
    ↓
[批量订单创建智能体] → 批量创建订单并返回结果
    ↓
[结果汇总智能体] → 汇总创建结果，生成报告
```

### 智能体详细说明

#### 1. 意图解析智能体 (IntentParserAgent)
- **功能**: 解析用户自然语言输入
- **输入**: 用户的自然语言描述
- **输出**: 结构化的意图数据
- **示例**:
  ```json
  {
    "quantity": 10,
    "order_type": "入库单",
    "customer": "自动化",
    "shop": "自动化",
    "status": "签到"
  }
  ```

#### 2. 数据查询智能体 (DataQueryAgent)
- **功能**: 从洞车系统查询基础数据
- **API调用**:
  - `query_customer()` - 查询商户信息
  - `query_shop()` - 查询店铺信息
  - `query_order_goods_list()` - 查询商品信息
- **输出**: 完整的商户、店铺、商品数据

#### 3. 参数构建智能体 (ParameterBuilderAgent)
- **功能**: 构建完整的订单创建参数
- **输入**: 意图数据 + 基础数据
- **输出**: 符合洞车API要求的订单参数
- **参数模板**:
  ```json
  {
    "associatedOrderNo": "",
    "customer": {
      "customerCode": "jr001744",
      "customerName": "商户名称",
      "shopCode": "218",
      "shopName": "店铺名称"
    },
    "orderGoodsList": [...],
    "pickup": true,
    "type": 1,
    "wordOrderReqList": [...]
  }
  ```

#### 4. 批量订单创建智能体 (BatchOrderCreatorAgent)
- **功能**: 执行实际的订单创建操作
- **特性**:
  - 支持批量创建
  - 自动生成唯一批次号
  - 错误处理和重试
  - 创建频率控制
- **API调用**: `create_order_rk()`

#### 5. 结果汇总智能体 (ResultSummaryAgent)
- **功能**: 生成用户友好的执行报告
- **输出**: 包含执行摘要、成功率、详细结果的报告

## 🚀 使用方法

### 1. API接口

#### 流式接口 (推荐)
```bash
POST /api/v1/ai-order
Content-Type: application/json

{
  "message": "请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到",
  "session_id": "optional_session_id"
}
```

**响应**: Server-Sent Events (SSE) 流式数据
```
data: {"step": "intent_parsing", "status": "start", "message": "🧠 正在解析您的需求..."}
data: {"step": "intent_parsing", "status": "complete", "result": {...}}
data: {"step": "data_query", "status": "start", "message": "🔍 正在查询基础数据..."}
...
```

#### 简单接口
```bash
POST /api/v1/ai-order-simple
Content-Type: application/json

{
  "message": "请帮我创建5个出库单，商户：测试商户",
  "session_id": "optional_session_id"
}
```

**响应**: 完整的结果数据
```json
{
  "success": true,
  "session_id": "ai_20250129_143022",
  "user_input": "请帮我创建5个出库单，商户：测试商户",
  "results": [...],
  "timestamp": "2025-01-29T14:30:22.123Z"
}
```

### 2. 前端界面

访问 `/order` 页面使用图形化界面：

- **智能对话区域**: 输入自然语言需求
- **实时进度显示**: 查看AI处理步骤
- **快捷模板**: 使用预设的常用模板
- **系统状态监控**: 查看智能体状态

### 3. 支持的语言格式

#### 基本格式
```
请帮我创建[数量]个[类型]，商户：[商户名]，店铺：[店铺名]，订单状态：[状态]
```

#### 示例
```
请帮我创建10个入库单，商户：自动化，店铺：自动化，订单状态：签到
请帮我创建5个出库单，商户：北京汇跃，店铺：梵客南店
创建20个入库单，商户编码：jr001744，状态：下发
帮我批量创建入库订单，数量：15个，商户：测试商户
```

#### 支持的参数

| 参数 | 说明 | 示例 |
|------|------|------|
| 数量 | 创建订单的数量 | 10个、5个、20个 |
| 类型 | 订单类型 | 入库单、出库单 |
| 商户 | 商户名称或编码 | 自动化、jr001744 |
| 店铺 | 店铺名称或编码 | 自动化、梵客南店 |
| 状态 | 订单状态 | 签到、下发、上架 |

## 🔧 配置说明

### 环境变量
```env
# LLM配置
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://api.deepseek.com/v1
OPENAI_MODEL=deepseek-chat

# 洞车API配置
DONGCHE_BASE_URL=http://tools-qa.jrdaimao.com/dataCreate/dongChe
DONGCHE_TIMEOUT=30
```

### 依赖安装
```bash
# 后端依赖
cd backend
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
```

## 📊 工作流程

### 1. 意图解析阶段
- 使用正则表达式和NLP技术提取关键信息
- 验证参数的完整性和有效性
- 生成结构化的意图数据

### 2. 数据查询阶段
- 根据商户名称查询商户信息
- 根据商户编码查询店铺列表
- 查询商户下的商品信息
- 验证数据的有效性

### 3. 参数构建阶段
- 选择合适的商户、店铺、商品
- 生成唯一的批次号
- 构建完整的订单参数
- 设置工单流程

### 4. 批量创建阶段
- 循环创建指定数量的订单
- 为每个订单生成唯一标识
- 处理创建过程中的异常
- 统计成功和失败数量

### 5. 结果汇总阶段
- 生成详细的执行报告
- 提供成功率统计
- 记录关键信息到记忆系统

## 🛠️ 扩展开发

### 添加新的智能体
```python
class CustomAgent:
    def __init__(self, model_client, memory_manager):
        self.model_client = model_client
        self.memory_manager = memory_manager
        
    async def initialize(self):
        # 初始化逻辑
        pass
        
    async def process(self, data, session_id):
        # 处理逻辑
        pass
```

### 自定义订单模板
```python
def build_custom_order_template(intent_data, base_data):
    return {
        "associatedOrderNo": "",
        "customer": {...},
        "orderGoodsList": [...],
        # 自定义字段
        "customField": "custom_value"
    }
```

### 添加新的API接口
```python
async def query_custom_data(param: str) -> Dict[str, Any]:
    """自定义数据查询接口"""
    url = f"{BASE_URL}/custom/endpoint"
    return await _make_request("POST", url, json_data={"param": param})
```

## 🔍 故障排除

### 常见问题

1. **智能体初始化失败**
   - 检查API Key配置
   - 确认网络连接
   - 查看日志错误信息

2. **数据查询失败**
   - 验证洞车API地址
   - 检查商户名称是否正确
   - 确认API权限

3. **订单创建失败**
   - 检查订单参数格式
   - 验证商品信息完整性
   - 查看API响应错误

### 调试方法

1. **启用详细日志**
   ```python
   logging.getLogger().setLevel(logging.DEBUG)
   ```

2. **检查智能体状态**
   ```bash
   GET /api/v1/agents
   ```

3. **测试单个接口**
   ```bash
   POST /api/v1/ai-order-simple
   ```

## 📈 性能优化

### 批量创建优化
- 控制并发数量避免API限制
- 添加适当的延迟间隔
- 实现错误重试机制

### 内存管理
- 定期清理过期会话数据
- 限制单次处理的最大数量
- 优化智能体间的数据传递

### API调用优化
- 实现请求缓存机制
- 使用连接池复用连接
- 添加超时和重试策略

## 🎉 总结

AI造数工具通过AutoGen智能体框架实现了从自然语言到订单创建的完整自动化流程。系统具有以下特点：

- **智能化**: 自然语言理解和处理
- **自动化**: 端到端的订单创建流程
- **可扩展**: 模块化的智能体架构
- **可靠性**: 完善的错误处理和重试机制
- **用户友好**: 直观的前端界面和实时反馈

通过这个工具，用户可以大大提高订单创建的效率，减少手动操作的错误，实现真正的智能化数据生成。
